# Target Recognition Tab Implementation

## Core Components

1. **Screenshot Management**
   - Upload button for new screenshots
   - Gallery view of existing screenshots
   - Delete/rename functionality

2. **Region Selection**
   - Interactive canvas for selecting regions
   - Ability to draw rectangles on screenshots
   - Zoom/pan functionality
   - Region labeling

3. **Detection Configuration**
   - Method selection (template, color, hybrid)
   - Confidence threshold slider
   - Color range selectors (HSV)
   - Template matching parameters

4. **Testing Tools**
   - Test detection button
   - Live preview with detection visualization
   - Performance metrics

## Data Structure

Screenshot data:
```python
{
  "screenshot_1701234567": {
    "name": "screenshot_1701234567",
    "image_data": "base64_encoded_data",
    "format": "png",
    "shape": [600, 800, 3],
    "created": 1701234567.89,
    "marked_regions": [
      {
        "label": "Enemy Type A",
        "type": "target",
        "coordinates": [100, 50, 200, 80],
        "detection_method": "template",
        "confidence_threshold": 0.8,
        "assigned_ability": "ability_1"
      }
    ]
  }
}
```

## Detection Methods

1. **Template Matching**
   - Uses OpenCV template matching
   - Configurable threshold
   - Multiple template support

2. **Color Detection**
   - HSV color range detection
   - Multiple color range support
   - Noise filtering options

3. **Hybrid Detection**
   - Combines template and color methods
   - Weighted confidence scoring
   - Higher accuracy but more CPU intensive