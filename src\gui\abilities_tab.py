"""
Abilities Tab for Auto Attack Bot
Configure all ability settings and parameters
"""

import tkinter as tk
from tkinter import ttk, messagebox

class AbilitiesTab:
    def __init__(self, parent, bot_engine, config_manager, logger):
        self.parent = parent
        self.bot_engine = bot_engine
        self.config_manager = config_manager
        self.logger = logger
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Create widgets
        self.create_widgets()
        
        # Load current settings
        self.load_abilities()
    
    def create_widgets(self):
        """Create abilities tab widgets"""
        # Configure grid
        self.frame.grid_columnconfigure(0, weight=1)
        self.frame.grid_rowconfigure(1, weight=1)
        
        # Header frame
        header_frame = ttk.Frame(self.frame)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        header_frame.grid_columnconfigure(2, weight=1)
        
        ttk.Label(header_frame, text="⚔️ Ability Configuration", font=("Arial", 14, "bold")).grid(
            row=0, column=0, sticky="w"
        )
        
        # Control buttons
        ttk.Button(header_frame, text="💾 Save All", command=self.save_all_abilities).grid(
            row=0, column=3, padx=5
        )
        ttk.Button(header_frame, text="📁 Load", command=self.load_abilities).grid(
            row=0, column=4, padx=5
        )
        ttk.Button(header_frame, text="🔄 Reset", command=self.reset_abilities).grid(
            row=0, column=5, padx=5
        )
        
        # Scrollable frame for abilities
        self.create_scrollable_frame()
        
        # Create ability configuration widgets
        self.create_ability_widgets()
    
    def create_scrollable_frame(self):
        """Create scrollable frame for abilities"""
        # Canvas and scrollbar
        canvas = tk.Canvas(self.frame)
        scrollbar = ttk.Scrollbar(self.frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.grid(row=1, column=0, sticky="nsew", padx=(10, 0), pady=(0, 10))
        scrollbar.grid(row=1, column=1, sticky="ns", pady=(0, 10))
        
        self.canvas = canvas
    
    def create_ability_widgets(self):
        """Create widgets for each ability"""
        abilities = self.config_manager.get("abilities", {})
        self.ability_widgets = {}
        
        row = 0
        for ability_key, ability_config in abilities.items():
            # Create frame for this ability
            ability_frame = ttk.LabelFrame(
                self.scrollable_frame, 
                text=f"Ability: {ability_config.get('key', ability_key).upper()}", 
                padding="10"
            )
            ability_frame.grid(row=row, column=0, sticky="ew", padx=5, pady=5)
            ability_frame.grid_columnconfigure(1, weight=1)
            ability_frame.grid_columnconfigure(3, weight=1)
            
            widgets = {}
            
            # Row 0: Enable checkbox and name
            widgets['enabled_var'] = tk.BooleanVar(value=ability_config.get("enabled", True))
            ttk.Checkbutton(
                ability_frame, 
                text="Enabled", 
                variable=widgets['enabled_var']
            ).grid(row=0, column=0, sticky="w")
            
            ttk.Label(ability_frame, text="Name:").grid(row=0, column=2, sticky="w", padx=(20, 5))
            widgets['name_var'] = tk.StringVar(value=ability_config.get("name", f"Ability {ability_key}"))
            ttk.Entry(ability_frame, textvariable=widgets['name_var'], width=20).grid(
                row=0, column=3, sticky="w"
            )
            
            # Row 1: Cooldown and Priority
            ttk.Label(ability_frame, text="Cooldown (s):").grid(row=1, column=0, sticky="w", pady=(5, 0))
            widgets['cooldown_var'] = tk.DoubleVar(value=ability_config.get("cooldown", 1.0))
            ttk.Spinbox(
                ability_frame, 
                from_=0.1, 
                to=60.0, 
                increment=0.1, 
                textvariable=widgets['cooldown_var'],
                width=8
            ).grid(row=1, column=1, sticky="w", pady=(5, 0))
            
            ttk.Label(ability_frame, text="Priority:").grid(row=1, column=2, sticky="w", padx=(20, 5), pady=(5, 0))
            widgets['priority_var'] = tk.IntVar(value=ability_config.get("priority", 3))
            ttk.Combobox(
                ability_frame, 
                textvariable=widgets['priority_var'],
                values=[1, 2, 3, 4, 5],
                width=5,
                state="readonly"
            ).grid(row=1, column=3, sticky="w", pady=(5, 0))
            
            # Row 2: Press count and Hold duration
            ttk.Label(ability_frame, text="Press Count:").grid(row=2, column=0, sticky="w", pady=(5, 0))
            widgets['press_count_var'] = tk.IntVar(value=ability_config.get("press_count", 1))
            ttk.Spinbox(
                ability_frame, 
                from_=1, 
                to=10, 
                textvariable=widgets['press_count_var'],
                width=8
            ).grid(row=2, column=1, sticky="w", pady=(5, 0))
            
            ttk.Label(ability_frame, text="Hold Duration (s):").grid(row=2, column=2, sticky="w", padx=(20, 5), pady=(5, 0))
            widgets['hold_duration_var'] = tk.DoubleVar(value=ability_config.get("hold_duration", 0.0))
            ttk.Spinbox(
                ability_frame, 
                from_=0.0, 
                to=5.0, 
                increment=0.1, 
                textvariable=widgets['hold_duration_var'],
                width=8
            ).grid(row=2, column=3, sticky="w", pady=(5, 0))
            
            # Row 3: AOE checkbox and Test button
            widgets['is_aoe_var'] = tk.BooleanVar(value=ability_config.get("is_aoe", False))
            ttk.Checkbutton(
                ability_frame, 
                text="Area of Effect (AOE)", 
                variable=widgets['is_aoe_var']
            ).grid(row=3, column=0, columnspan=2, sticky="w", pady=(5, 0))
            
            ttk.Button(
                ability_frame, 
                text="🧪 Test", 
                command=lambda ak=ability_key: self.test_ability(ak)
            ).grid(row=3, column=3, sticky="e", pady=(5, 0))
            
            self.ability_widgets[ability_key] = widgets
            row += 1
    
    def save_all_abilities(self):
        """Save all ability configurations"""
        try:
            for ability_key, widgets in self.ability_widgets.items():
                config = {
                    "name": widgets['name_var'].get(),
                    "key": ability_key.split('_')[1] if '_' in ability_key else ability_key,
                    "enabled": widgets['enabled_var'].get(),
                    "cooldown": widgets['cooldown_var'].get(),
                    "priority": widgets['priority_var'].get(),
                    "press_count": widgets['press_count_var'].get(),
                    "hold_duration": widgets['hold_duration_var'].get(),
                    "is_aoe": widgets['is_aoe_var'].get(),
                    "random_timing": True,
                    "timing_variance": 0.2
                }
                
                self.config_manager.set_ability_config(ability_key, config)
            
            self.config_manager.save_config()
            messagebox.showinfo("Success", "All abilities saved successfully!")
            self.logger.info("All abilities saved from abilities tab")
            
        except Exception as e:
            self.logger.error(f"Error saving abilities: {e}")
            messagebox.showerror("Error", f"Error saving abilities: {e}")
    
    def load_abilities(self):
        """Load abilities from configuration"""
        try:
            abilities = self.config_manager.get("abilities", {})
            
            for ability_key, ability_config in abilities.items():
                if ability_key in self.ability_widgets:
                    widgets = self.ability_widgets[ability_key]
                    widgets['name_var'].set(ability_config.get("name", f"Ability {ability_key}"))
                    widgets['enabled_var'].set(ability_config.get("enabled", True))
                    widgets['cooldown_var'].set(ability_config.get("cooldown", 1.0))
                    widgets['priority_var'].set(ability_config.get("priority", 3))
                    widgets['press_count_var'].set(ability_config.get("press_count", 1))
                    widgets['hold_duration_var'].set(ability_config.get("hold_duration", 0.0))
                    widgets['is_aoe_var'].set(ability_config.get("is_aoe", False))
            
            self.logger.info("Abilities loaded from configuration")
            
        except Exception as e:
            self.logger.error(f"Error loading abilities: {e}")
            messagebox.showerror("Error", f"Error loading abilities: {e}")
    
    def reset_abilities(self):
        """Reset all abilities to default values"""
        if messagebox.askyesno("Reset Abilities", "Are you sure you want to reset all abilities to default values?"):
            try:
                # Load default config
                default_abilities = self.config_manager._load_default_config()["abilities"]
                
                for ability_key, default_config in default_abilities.items():
                    self.config_manager.set_ability_config(ability_key, default_config)
                
                # Reload the UI
                self.load_abilities()
                
                messagebox.showinfo("Success", "All abilities reset to default values!")
                self.logger.info("All abilities reset to defaults")
                
            except Exception as e:
                self.logger.error(f"Error resetting abilities: {e}")
                messagebox.showerror("Error", f"Error resetting abilities: {e}")
    
    def test_ability(self, ability_key):
        """Test a specific ability"""
        try:
            if hasattr(self.bot_engine, 'ability_manager'):
                success = self.bot_engine.ability_manager.test_ability(ability_key)
                if success:
                    self.logger.info(f"Tested ability {ability_key}")
                else:
                    messagebox.showerror("Error", f"Failed to test ability {ability_key}")
            else:
                messagebox.showerror("Error", "Ability manager not available")
                
        except Exception as e:
            self.logger.error(f"Error testing ability {ability_key}: {e}")
            messagebox.showerror("Error", f"Error testing ability: {e}")
    
    def refresh(self):
        """Refresh the abilities tab"""
        self.load_abilities()
