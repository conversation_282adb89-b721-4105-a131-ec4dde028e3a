# Abilities Tab Implementation

## Core Components

1. **Scrollable Frame**
   - Contains all ability configurations
   - Allows for many abilities without UI clutter

2. **Per-Ability Configuration**
   - Each ability (1,2,3,4,5,e,x,c,q,g,t,r) gets:
     - Enable/disable checkbox
     - Name field
     - Cooldown slider (0.5-60s)
     - Priority dropdown (1-5)
     - Damage slider (10-1000)
     - Range slider (50-1000)
     - AOE checkbox
     - Press count spinner (1-10)
     - Hold duration slider (0-5s)
     - Test button

3. **Control Buttons**
   - Save All Abilities
   - Load Abilities
   - Test All Enabled
   - Reset to Defaults

## Data Structure

Each ability stores:
```python
{
  "ability_1": {
    "name": "Primary Attack",
    "key": "1",
    "cooldown": 1.0,
    "priority": 4,
    "damage": 80,
    "range": 250,
    "press_count": 1,
    "hold_duration": 0.0,
    "is_aoe": False,
    "enabled": True
  }
}
```

## Default Configurations

- **1**: Primary Attack (1.0s cooldown)
- **2**: Secondary Attack (3.0s cooldown)
- **3**: Special Ability (5.0s cooldown, AOE)
- **4**: Power Strike (8.0s cooldown)
- **5**: Ultimate (15.0s cooldown, AOE)
- **e**: Quick Skill (2.0s cooldown)
- **x**: Burst (12.0s cooldown, AOE)
- **c**: Combo (4.0s cooldown, 3 presses)
- **q**: Quick Cast (6.0s cooldown)
- **g**: Ground Slam (10.0s cooldown, 1.0s hold, AOE)
- **t**: Tactical (7.0s cooldown, 2 presses)
- **r**: Reload/Refresh (3.0s cooldown, 0.5s hold)