"""
Configuration Manager for Auto Attack Bot
Handles saving/loading of all bot settings and configurations
"""

import json
import os
from typing import Dict, Any, Optional
from datetime import datetime

class ConfigManager:
    def __init__(self, config_file: str = "data/config.json"):
        self.config_file = config_file
        self.config = self._load_default_config()
        self.load_config()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration"""
        return {
            "version": "1.0",
            "last_updated": datetime.now().isoformat(),
            
            # General Settings
            "general": {
                "game_window_name": "",
                "monitor_fps": 30,
                "safety_delay": 0.1,
                "emergency_stop_key": "F12"
            },
            
            # Abilities Configuration
            "abilities": {
                "ability_1": {
                    "name": "Primary Attack",
                    "key": "1",
                    "cooldown": 1.0,
                    "priority": 4,
                    "damage": 80,
                    "range": 250,
                    "press_count": 1,
                    "hold_duration": 0.0,
                    "is_aoe": False,
                    "enabled": True,
                    "random_timing": True,
                    "timing_variance": 0.2
                },
                "ability_2": {
                    "name": "Secondary Attack",
                    "key": "2",
                    "cooldown": 3.0,
                    "priority": 3,
                    "damage": 120,
                    "range": 300,
                    "press_count": 1,
                    "hold_duration": 0.0,
                    "is_aoe": False,
                    "enabled": True,
                    "random_timing": True,
                    "timing_variance": 0.3
                },
                "ability_3": {
                    "name": "Special Ability",
                    "key": "3",
                    "cooldown": 5.0,
                    "priority": 2,
                    "damage": 200,
                    "range": 400,
                    "press_count": 1,
                    "hold_duration": 0.0,
                    "is_aoe": True,
                    "enabled": True,
                    "random_timing": True,
                    "timing_variance": 0.5
                },
                "ability_4": {
                    "name": "Power Strike",
                    "key": "4",
                    "cooldown": 8.0,
                    "priority": 2,
                    "damage": 300,
                    "range": 200,
                    "press_count": 1,
                    "hold_duration": 0.0,
                    "is_aoe": False,
                    "enabled": True,
                    "random_timing": True,
                    "timing_variance": 0.4
                },
                "ability_5": {
                    "name": "Ultimate",
                    "key": "5",
                    "cooldown": 15.0,
                    "priority": 1,
                    "damage": 500,
                    "range": 500,
                    "press_count": 1,
                    "hold_duration": 0.0,
                    "is_aoe": True,
                    "enabled": True,
                    "random_timing": True,
                    "timing_variance": 0.8
                },
                "ability_e": {
                    "name": "Quick Skill",
                    "key": "e",
                    "cooldown": 2.0,
                    "priority": 3,
                    "damage": 60,
                    "range": 200,
                    "press_count": 1,
                    "hold_duration": 0.0,
                    "is_aoe": False,
                    "enabled": True,
                    "random_timing": True,
                    "timing_variance": 0.2
                },
                "ability_x": {
                    "name": "Burst",
                    "key": "x",
                    "cooldown": 12.0,
                    "priority": 1,
                    "damage": 400,
                    "range": 350,
                    "press_count": 1,
                    "hold_duration": 0.0,
                    "is_aoe": True,
                    "enabled": True,
                    "random_timing": True,
                    "timing_variance": 0.6
                },
                "ability_c": {
                    "name": "Combo",
                    "key": "c",
                    "cooldown": 4.0,
                    "priority": 3,
                    "damage": 150,
                    "range": 250,
                    "press_count": 3,
                    "hold_duration": 0.0,
                    "is_aoe": False,
                    "enabled": True,
                    "random_timing": True,
                    "timing_variance": 0.3
                },
                "ability_q": {
                    "name": "Quick Cast",
                    "key": "q",
                    "cooldown": 6.0,
                    "priority": 2,
                    "damage": 180,
                    "range": 300,
                    "press_count": 1,
                    "hold_duration": 0.0,
                    "is_aoe": False,
                    "enabled": True,
                    "random_timing": True,
                    "timing_variance": 0.4
                },
                "ability_t": {
                    "name": "Tactical",
                    "key": "t",
                    "cooldown": 7.0,
                    "priority": 2,
                    "damage": 220,
                    "range": 280,
                    "press_count": 2,
                    "hold_duration": 0.0,
                    "is_aoe": False,
                    "enabled": True,
                    "random_timing": True,
                    "timing_variance": 0.4
                },
                "ability_r": {
                    "name": "Reload/Refresh",
                    "key": "r",
                    "cooldown": 3.0,
                    "priority": 4,
                    "damage": 0,
                    "range": 0,
                    "press_count": 1,
                    "hold_duration": 0.5,
                    "is_aoe": False,
                    "enabled": True,
                    "random_timing": True,
                    "timing_variance": 0.2
                }
            },
            
            # Auto Click Settings
            "auto_click": {
                "enabled": False,
                "min_interval": 0.5,
                "max_interval": 3.0,
                "click_type": "left"  # left, right, middle
            },
            
            # Target Recognition
            "target_recognition": {
                "method": "template",  # template, color, hybrid
                "confidence_threshold": 0.8,
                "scan_region": [0, 0, 1920, 1080],
                "templates": {},
                "color_ranges": {}
            },
            
            # Performance Settings
            "performance": {
                "detection_fps": 10,
                "ability_check_interval": 0.1,
                "max_cpu_usage": 80
            }
        }
    
    def load_config(self) -> bool:
        """Load configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    self._merge_config(self.config, loaded_config)
                return True
        except Exception as e:
            print(f"Error loading config: {e}")
        return False
    
    def save_config(self) -> bool:
        """Save current configuration to file"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            self.config["last_updated"] = datetime.now().isoformat()
            
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def _merge_config(self, default: Dict, loaded: Dict):
        """Recursively merge loaded config with defaults"""
        for key, value in loaded.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self._merge_config(default[key], value)
                else:
                    default[key] = value
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation (e.g., 'abilities.ability_1.cooldown')"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value: Any):
        """Set configuration value using dot notation"""
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def get_ability_config(self, ability_key: str) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific ability"""
        return self.config["abilities"].get(ability_key)
    
    def set_ability_config(self, ability_key: str, config: Dict[str, Any]):
        """Set configuration for a specific ability"""
        self.config["abilities"][ability_key] = config
    
    def export_config(self, filepath: str) -> bool:
        """Export configuration to a file"""
        try:
            with open(filepath, 'w') as f:
                json.dump(self.config, f, indent=2)
            return True
        except Exception as e:
            print(f"Error exporting config: {e}")
            return False
    
    def import_config(self, filepath: str) -> bool:
        """Import configuration from a file"""
        try:
            with open(filepath, 'r') as f:
                imported_config = json.load(f)
                self._merge_config(self.config, imported_config)
            return True
        except Exception as e:
            print(f"Error importing config: {e}")
            return False
