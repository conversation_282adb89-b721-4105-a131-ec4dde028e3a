"""
Target Detection System for Auto Attack Bot
Handles screenshot capture and target recognition using OpenCV
"""

import cv2
import numpy as np
import pyautogui
import time
from typing import List, Dict, Tuple, Optional
import base64
from io import BytesIO
from PIL import Image

from src.utils.config_manager import Config<PERSON>anager
from src.utils.logger import Bo<PERSON><PERSON>ogger

class TargetDetector:
    def __init__(self, config_manager: ConfigManager, logger: BotLogger):
        self.config_manager = config_manager
        self.logger = logger
        
        # Disable pyautogui failsafe for automated operation
        pyautogui.FAILSAFE = False
        
        # Cache for templates
        self.template_cache = {}
        self.last_screenshot = None
        self.last_screenshot_time = 0
        
        self.logger.info("Target detector initialized")
    
    def detect_targets(self) -> List[Dict]:
        """Main target detection method"""
        try:
            # Capture screenshot
            screenshot = self._capture_screenshot()
            if screenshot is None:
                return []
            
            # Get detection method from config
            method = self.config_manager.get("target_recognition.method", "template")
            
            targets = []
            if method == "template":
                targets = self._detect_by_template(screenshot)
            elif method == "color":
                targets = self._detect_by_color(screenshot)
            elif method == "hybrid":
                targets = self._detect_hybrid(screenshot)
            
            return targets
            
        except Exception as e:
            self.logger.error(f"Error in target detection: {e}")
            return []
    
    def _capture_screenshot(self) -> Optional[np.ndarray]:
        """Capture screenshot of the game area"""
        try:
            # Get scan region from config
            scan_region = self.config_manager.get("target_recognition.scan_region", [0, 0, 1920, 1080])
            x, y, width, height = scan_region
            
            # Capture screenshot
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            
            # Convert to OpenCV format
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            self.last_screenshot = screenshot_cv
            self.last_screenshot_time = time.time()
            
            return screenshot_cv
            
        except Exception as e:
            self.logger.error(f"Error capturing screenshot: {e}")
            return None
    
    def _detect_by_template(self, screenshot: np.ndarray) -> List[Dict]:
        """Detect targets using template matching"""
        targets = []
        
        try:
            # Get templates from config
            templates_config = self.config_manager.get("target_recognition.templates", {})
            confidence_threshold = self.config_manager.get("target_recognition.confidence_threshold", 0.8)
            
            for template_name, template_data in templates_config.items():
                if not template_data.get("enabled", True):
                    continue
                
                # Load template from cache or decode
                template = self._get_template(template_name, template_data)
                if template is None:
                    continue
                
                # Perform template matching
                result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
                locations = np.where(result >= confidence_threshold)
                
                # Convert matches to target objects
                for pt in zip(*locations[::-1]):
                    target = {
                        "name": template_name,
                        "type": "template",
                        "position": pt,
                        "confidence": float(result[pt[1], pt[0]]),
                        "size": template.shape[:2],
                        "assigned_ability": template_data.get("assigned_ability", "")
                    }
                    targets.append(target)
            
        except Exception as e:
            self.logger.error(f"Error in template detection: {e}")
        
        return targets
    
    def _detect_by_color(self, screenshot: np.ndarray) -> List[Dict]:
        """Detect targets using color range detection"""
        targets = []
        
        try:
            # Get color ranges from config
            color_ranges = self.config_manager.get("target_recognition.color_ranges", {})
            
            # Convert screenshot to HSV for better color detection
            hsv = cv2.cvtColor(screenshot, cv2.COLOR_BGR2HSV)
            
            for range_name, range_data in color_ranges.items():
                if not range_data.get("enabled", True):
                    continue
                
                # Create mask for color range
                lower = np.array(range_data.get("lower_hsv", [0, 0, 0]))
                upper = np.array(range_data.get("upper_hsv", [179, 255, 255]))
                mask = cv2.inRange(hsv, lower, upper)
                
                # Find contours
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # Filter contours by size
                min_area = range_data.get("min_area", 100)
                max_area = range_data.get("max_area", 10000)
                
                for contour in contours:
                    area = cv2.contourArea(contour)
                    if min_area <= area <= max_area:
                        # Get bounding rectangle
                        x, y, w, h = cv2.boundingRect(contour)
                        
                        target = {
                            "name": range_name,
                            "type": "color",
                            "position": (x, y),
                            "size": (w, h),
                            "area": area,
                            "assigned_ability": range_data.get("assigned_ability", "")
                        }
                        targets.append(target)
            
        except Exception as e:
            self.logger.error(f"Error in color detection: {e}")
        
        return targets
    
    def _detect_hybrid(self, screenshot: np.ndarray) -> List[Dict]:
        """Detect targets using both template and color methods"""
        template_targets = self._detect_by_template(screenshot)
        color_targets = self._detect_by_color(screenshot)
        
        # Combine and deduplicate targets
        all_targets = template_targets + color_targets
        
        # Simple deduplication based on proximity
        deduplicated = []
        for target in all_targets:
            is_duplicate = False
            for existing in deduplicated:
                if self._targets_overlap(target, existing):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                deduplicated.append(target)
        
        return deduplicated
    
    def _get_template(self, template_name: str, template_data: Dict) -> Optional[np.ndarray]:
        """Get template image from cache or load from data"""
        if template_name in self.template_cache:
            return self.template_cache[template_name]
        
        try:
            # Decode base64 image data
            image_data = template_data.get("image_data", "")
            if not image_data:
                return None
            
            # Decode base64
            image_bytes = base64.b64decode(image_data)
            image = Image.open(BytesIO(image_bytes))
            
            # Convert to OpenCV format
            template = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Cache the template
            self.template_cache[template_name] = template
            
            return template
            
        except Exception as e:
            self.logger.error(f"Error loading template {template_name}: {e}")
            return None
    
    def _targets_overlap(self, target1: Dict, target2: Dict, threshold: int = 50) -> bool:
        """Check if two targets overlap within a threshold"""
        pos1 = target1["position"]
        pos2 = target2["position"]
        
        distance = np.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)
        return distance < threshold
    
    def add_template(self, name: str, image_data: str, region: Tuple[int, int, int, int], 
                    assigned_ability: str = "") -> bool:
        """Add a new template for detection"""
        try:
            template_config = {
                "name": name,
                "image_data": image_data,
                "region": region,
                "assigned_ability": assigned_ability,
                "enabled": True,
                "confidence_threshold": 0.8
            }
            
            # Save to config
            templates = self.config_manager.get("target_recognition.templates", {})
            templates[name] = template_config
            self.config_manager.set("target_recognition.templates", templates)
            
            # Clear cache to force reload
            if name in self.template_cache:
                del self.template_cache[name]
            
            self.logger.info(f"Template '{name}' added successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding template: {e}")
            return False
    
    def remove_template(self, name: str) -> bool:
        """Remove a template"""
        try:
            templates = self.config_manager.get("target_recognition.templates", {})
            if name in templates:
                del templates[name]
                self.config_manager.set("target_recognition.templates", templates)
                
                # Remove from cache
                if name in self.template_cache:
                    del self.template_cache[name]
                
                self.logger.info(f"Template '{name}' removed successfully")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Error removing template: {e}")
            return False
    
    def test_detection(self) -> Tuple[List[Dict], Optional[np.ndarray]]:
        """Test current detection settings and return results with screenshot"""
        screenshot = self._capture_screenshot()
        if screenshot is None:
            return [], None
        
        targets = self.detect_targets()
        return targets, screenshot
    
    def get_last_screenshot(self) -> Optional[np.ndarray]:
        """Get the last captured screenshot"""
        return self.last_screenshot
