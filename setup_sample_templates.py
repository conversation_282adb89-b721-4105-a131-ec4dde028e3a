#!/usr/bin/env python3
"""
Setup Sample Templates for Auto Attack Bot
Creates sample target templates for testing
"""

import base64
import json
import os
from src.utils.config_manager import ConfigManager
from src.utils.logger import setup_logger

def create_sample_templates():
    """Create sample templates for testing"""
    
    # Initialize components
    config_manager = ConfigManager()
    logger = setup_logger()
    
    logger.info("Setting up sample templates...")
    
    # Sample template data (small colored rectangles for testing)
    sample_templates = {
        "red_enemy": {
            "name": "Red Enemy",
            "image_data": create_sample_image_data("red"),
            "region": [100, 100, 50, 50],
            "assigned_ability": "ability_1",
            "detection_method": "template",
            "confidence_threshold": 0.7,
            "enabled": True
        },
        "blue_enemy": {
            "name": "Blue Enemy", 
            "image_data": create_sample_image_data("blue"),
            "region": [200, 150, 60, 40],
            "assigned_ability": "ability_2",
            "detection_method": "template",
            "confidence_threshold": 0.8,
            "enabled": True
        },
        "boss_enemy": {
            "name": "Boss Enemy",
            "image_data": create_sample_image_data("purple"),
            "region": [300, 200, 80, 80],
            "assigned_ability": "ability_5",
            "detection_method": "template", 
            "confidence_threshold": 0.9,
            "enabled": True
        }
    }
    
    # Save templates to config
    config_manager.set("target_recognition.templates", sample_templates)
    
    # Also create some sample color ranges
    color_ranges = {
        "red_targets": {
            "name": "Red Targets",
            "lower_hsv": [0, 100, 100],
            "upper_hsv": [10, 255, 255],
            "min_area": 100,
            "max_area": 5000,
            "assigned_ability": "ability_1",
            "enabled": True
        },
        "blue_targets": {
            "name": "Blue Targets",
            "lower_hsv": [100, 100, 100],
            "upper_hsv": [130, 255, 255],
            "min_area": 150,
            "max_area": 4000,
            "assigned_ability": "ability_2",
            "enabled": True
        }
    }
    
    config_manager.set("target_recognition.color_ranges", color_ranges)
    
    # Save configuration
    config_manager.save_config()
    
    logger.info(f"Created {len(sample_templates)} sample templates")
    logger.info(f"Created {len(color_ranges)} color detection ranges")
    logger.info("Sample templates setup complete!")
    
    return True

def create_sample_image_data(color):
    """Create a small sample image for testing"""
    try:
        import cv2
        import numpy as np
        
        # Create a small colored rectangle
        if color == "red":
            bgr_color = (0, 0, 255)  # Red in BGR
        elif color == "blue":
            bgr_color = (255, 0, 0)  # Blue in BGR
        elif color == "purple":
            bgr_color = (255, 0, 255)  # Purple in BGR
        else:
            bgr_color = (128, 128, 128)  # Gray default
        
        # Create 30x30 colored rectangle
        image = np.full((30, 30, 3), bgr_color, dtype=np.uint8)
        
        # Add a border
        cv2.rectangle(image, (0, 0), (29, 29), (255, 255, 255), 2)
        
        # Encode to base64
        _, buffer = cv2.imencode('.png', image)
        image_data = base64.b64encode(buffer).decode('utf-8')
        
        return image_data
        
    except Exception as e:
        print(f"Error creating sample image: {e}")
        return ""

def setup_enhanced_abilities():
    """Setup enhanced ability configurations"""
    
    config_manager = ConfigManager()
    logger = setup_logger()
    
    logger.info("Setting up enhanced ability configurations...")
    
    # Enhanced ability configurations with better defaults
    enhanced_abilities = {
        "ability_1": {
            "name": "Quick Strike",
            "key": "1",
            "cooldown": 0.8,
            "priority": 5,
            "damage": 100,
            "range": 200,
            "press_count": 1,
            "hold_duration": 0.0,
            "is_aoe": False,
            "enabled": True,
            "random_timing": True,
            "timing_variance": 0.1
        },
        "ability_2": {
            "name": "Power Shot",
            "key": "2",
            "cooldown": 2.5,
            "priority": 4,
            "damage": 180,
            "range": 350,
            "press_count": 1,
            "hold_duration": 0.0,
            "is_aoe": False,
            "enabled": True,
            "random_timing": True,
            "timing_variance": 0.3
        },
        "ability_3": {
            "name": "Area Blast",
            "key": "3",
            "cooldown": 4.0,
            "priority": 3,
            "damage": 250,
            "range": 400,
            "press_count": 1,
            "hold_duration": 0.0,
            "is_aoe": True,
            "enabled": True,
            "random_timing": True,
            "timing_variance": 0.4
        },
        "ability_4": {
            "name": "Heavy Strike",
            "key": "4",
            "cooldown": 6.0,
            "priority": 2,
            "damage": 350,
            "range": 250,
            "press_count": 1,
            "hold_duration": 0.0,
            "is_aoe": False,
            "enabled": True,
            "random_timing": True,
            "timing_variance": 0.5
        },
        "ability_5": {
            "name": "Ultimate Devastation",
            "key": "5",
            "cooldown": 12.0,
            "priority": 1,
            "damage": 600,
            "range": 500,
            "press_count": 1,
            "hold_duration": 0.0,
            "is_aoe": True,
            "enabled": True,
            "random_timing": True,
            "timing_variance": 0.8
        },
        "ability_e": {
            "name": "Evasion",
            "key": "e",
            "cooldown": 3.0,
            "priority": 4,
            "damage": 0,
            "range": 0,
            "press_count": 1,
            "hold_duration": 0.0,
            "is_aoe": False,
            "enabled": True,
            "random_timing": True,
            "timing_variance": 0.2
        },
        "ability_x": {
            "name": "Explosive Burst",
            "key": "x",
            "cooldown": 8.0,
            "priority": 2,
            "damage": 450,
            "range": 350,
            "press_count": 1,
            "hold_duration": 0.0,
            "is_aoe": True,
            "enabled": True,
            "random_timing": True,
            "timing_variance": 0.6
        },
        "ability_c": {
            "name": "Combo Attack",
            "key": "c",
            "cooldown": 5.0,
            "priority": 3,
            "damage": 200,
            "range": 300,
            "press_count": 3,
            "hold_duration": 0.0,
            "is_aoe": False,
            "enabled": True,
            "random_timing": True,
            "timing_variance": 0.3
        },
        "ability_q": {
            "name": "Quick Cast",
            "key": "q",
            "cooldown": 4.5,
            "priority": 3,
            "damage": 220,
            "range": 320,
            "press_count": 1,
            "hold_duration": 0.0,
            "is_aoe": False,
            "enabled": True,
            "random_timing": True,
            "timing_variance": 0.4
        },
        "ability_t": {
            "name": "Tactical Strike",
            "key": "t",
            "cooldown": 7.0,
            "priority": 2,
            "damage": 280,
            "range": 280,
            "press_count": 2,
            "hold_duration": 0.0,
            "is_aoe": False,
            "enabled": True,
            "random_timing": True,
            "timing_variance": 0.4
        },
        "ability_r": {
            "name": "Reload/Refresh",
            "key": "r",
            "cooldown": 2.0,
            "priority": 5,
            "damage": 0,
            "range": 0,
            "press_count": 1,
            "hold_duration": 0.3,
            "is_aoe": False,
            "enabled": True,
            "random_timing": True,
            "timing_variance": 0.1
        }
    }
    
    # Update abilities in config
    for ability_key, ability_config in enhanced_abilities.items():
        config_manager.set_ability_config(ability_key, ability_config)
    
    # Save configuration
    config_manager.save_config()
    
    logger.info(f"Enhanced {len(enhanced_abilities)} ability configurations")
    logger.info("Enhanced abilities setup complete!")
    
    return True

if __name__ == "__main__":
    print("🎯 Setting up Auto Attack Bot sample configuration...")
    
    # Setup sample templates
    if create_sample_templates():
        print("✅ Sample templates created successfully!")
    else:
        print("❌ Failed to create sample templates")
    
    # Setup enhanced abilities
    if setup_enhanced_abilities():
        print("✅ Enhanced abilities configured successfully!")
    else:
        print("❌ Failed to configure enhanced abilities")
    
    print("\n🚀 Setup complete! Your bot is now ready with:")
    print("   • 3 Sample target templates (Red, Blue, Purple enemies)")
    print("   • 2 Color detection ranges")
    print("   • 11 Enhanced ability configurations")
    print("   • Optimized timing and priority settings")
    print("\nYou can now:")
    print("   1. Start the bot with 'python main.py'")
    print("   2. Go to Target Recognition tab to see templates")
    print("   3. Go to Abilities tab to see enhanced configs")
    print("   4. Use Control tab to start botting!")
