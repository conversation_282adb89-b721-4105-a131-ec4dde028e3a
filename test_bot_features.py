#!/usr/bin/env python3
"""
Test Bot Features - Comprehensive Testing Script
Tests all major bot functionality
"""

import time
import sys
import os

# Add src to path
sys.path.append('.')

from src.utils.config_manager import ConfigManager
from src.utils.logger import setup_logger
from src.core.bot_engine import Bot<PERSON>ngine
from src.core.target_detector import TargetDetector
from src.core.ability_manager import AbilityManager
from src.core.input_controller import Input<PERSON>ontroller

def test_configuration_system():
    """Test configuration loading and saving"""
    print("\n🔧 Testing Configuration System...")
    
    try:
        config_manager = ConfigManager()
        logger = setup_logger()
        
        # Test basic config operations
        config_manager.set("test.value", "hello_world")
        assert config_manager.get("test.value") == "hello_world"
        
        # Test ability config
        test_ability = {
            "name": "Test Ability",
            "key": "t",
            "cooldown": 5.0,
            "enabled": True
        }
        config_manager.set_ability_config("test_ability", test_ability)
        loaded_ability = config_manager.get_ability_config("test_ability")
        assert loaded_ability["name"] == "Test Ability"
        
        # Test save/load
        config_manager.save_config()
        
        print("✅ Configuration system working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration system error: {e}")
        return False

def test_input_controller():
    """Test input controller functionality"""
    print("\n⌨️ Testing Input Controller...")
    
    try:
        logger = setup_logger()
        controller = InputController(logger)
        
        print("   Testing key press simulation...")
        # Test a safe key that won't interfere
        controller.key_press("f1")  # F1 is usually safe
        time.sleep(0.1)
        
        print("   Testing mouse operations...")
        # Get current position and move slightly
        current_pos = controller.get_mouse_position()
        controller.move_mouse(current_pos[0] + 1, current_pos[1] + 1)
        controller.move_mouse(current_pos[0], current_pos[1])  # Move back
        
        print("✅ Input controller working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Input controller error: {e}")
        return False

def test_ability_manager():
    """Test ability management system"""
    print("\n⚔️ Testing Ability Manager...")
    
    try:
        config_manager = ConfigManager()
        logger = setup_logger()
        ability_manager = AbilityManager(config_manager, logger)
        
        # Test ability status
        abilities_status = ability_manager.get_all_abilities_status()
        print(f"   Found {len(abilities_status)} configured abilities")
        
        # Test ability priorities
        priorities = ability_manager.get_ability_priorities()
        print(f"   Ability priority order: {priorities[:3]}...")  # Show first 3
        
        # Test cooldown system
        test_ability = "ability_1"
        if test_ability in abilities_status:
            status = ability_manager.get_ability_status(test_ability)
            print(f"   {test_ability} status: Ready={status['is_ready']}, Cooldown={status['cooldown']}s")
        
        print("✅ Ability manager working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Ability manager error: {e}")
        return False

def test_target_detector():
    """Test target detection system"""
    print("\n🎯 Testing Target Detector...")
    
    try:
        config_manager = ConfigManager()
        logger = setup_logger()
        detector = TargetDetector(config_manager, logger)
        
        # Test template loading
        templates = config_manager.get("target_recognition.templates", {})
        print(f"   Found {len(templates)} configured templates")
        
        for template_name in list(templates.keys())[:2]:  # Test first 2
            print(f"   Template: {template_name}")
        
        # Test detection (will return empty if no targets visible)
        print("   Testing detection system...")
        targets = detector.detect_targets()
        print(f"   Detection test completed - found {len(targets)} targets")
        
        print("✅ Target detector working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Target detector error: {e}")
        return False

def test_bot_engine():
    """Test main bot engine"""
    print("\n🤖 Testing Bot Engine...")
    
    try:
        config_manager = ConfigManager()
        logger = setup_logger()
        bot_engine = BotEngine(config_manager, logger)
        
        # Test bot state
        print(f"   Bot running: {bot_engine.is_running}")
        print(f"   Bot paused: {bot_engine.is_paused}")
        
        # Test statistics
        stats = bot_engine.get_stats()
        print(f"   Stats initialized: {len(stats)} metrics tracked")
        
        # Test start/stop (very briefly)
        print("   Testing bot start/stop...")
        if bot_engine.start():
            print("   ✓ Bot started successfully")
            time.sleep(0.5)  # Run very briefly
            if bot_engine.stop():
                print("   ✓ Bot stopped successfully")
            else:
                print("   ⚠️ Bot stop had issues")
        else:
            print("   ⚠️ Bot start had issues")
        
        print("✅ Bot engine working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Bot engine error: {e}")
        return False

def test_file_structure():
    """Test that all required files exist"""
    print("\n📁 Testing File Structure...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "src/core/bot_engine.py",
        "src/core/target_detector.py", 
        "src/core/ability_manager.py",
        "src/core/input_controller.py",
        "src/gui/main_window.py",
        "src/gui/control_tab.py",
        "src/gui/abilities_tab.py",
        "src/gui/target_recognition_tab.py",
        "src/gui/configuration_tab.py",
        "src/gui/logs_tab.py",
        "src/gui/monitoring_panel.py",
        "src/gui/template_editor.py",
        "src/utils/config_manager.py",
        "src/utils/logger.py"
    ]
    
    required_dirs = [
        "src",
        "src/core",
        "src/gui", 
        "src/utils",
        "data",
        "logs"
    ]
    
    missing_files = []
    missing_dirs = []
    
    # Check directories
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
    
    # Check files
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_dirs:
        print(f"   ❌ Missing directories: {missing_dirs}")
        return False
    
    if missing_files:
        print(f"   ❌ Missing files: {missing_files}")
        return False
    
    print(f"   ✅ All {len(required_files)} files present")
    print(f"   ✅ All {len(required_dirs)} directories present")
    return True

def run_comprehensive_test():
    """Run all tests"""
    print("🧪 Auto Attack Bot - Comprehensive Feature Test")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Configuration System", test_configuration_system),
        ("Input Controller", test_input_controller),
        ("Ability Manager", test_ability_manager),
        ("Target Detector", test_target_detector),
        ("Bot Engine", test_bot_engine)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("🏁 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Your bot is fully functional!")
        print("\n🚀 Ready to start botting:")
        print("   1. Run: python main.py")
        print("   2. Configure your targets in Target Recognition tab")
        print("   3. Adjust abilities in Abilities tab")
        print("   4. Start botting in Control tab")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
