# 🤖 Auto Attack Bot - Rebuild Guide

## Core Functionality

The Auto Attack Bot provides:
- Target detection using computer vision
- Automated ability usage with configurable keys
- Customizable press counts and hold durations
- Real-time monitoring and statistics
- Safety features and emergency stops

## Tab Structure & Functionality

### 1️⃣ Control Tab
- **Purpose**: Main bot control interface
- **Features**:
  - START/STOP buttons
  - Emergency stop button
  - Status indicators
  - Performance metrics
  - Quick access to common settings

### 2️⃣ Abilities Tab
- **Purpose**: Configure all ability keys
- **Features**:
  - Support for keys: 1,2,3,4,5,e,x,c,q,g,t,r
  - Enable/disable individual abilities
  - Set custom names
  - Configure cooldowns (0.5-60s)
  - Set priority levels (1-5)
  - Set press count (1-10)
  - Set hold duration (0-5s)
  - Individual ability testing
  - Save/load/reset functionality

### 3️⃣ Target Recognition Tab
- **Purpose**: Configure target detection
- **Features**:
  - Screenshot upload
  - Target region selection
  - Template matching configuration
  - Color detection settings
  - Test detection button
  - Save templates functionality

### 4️⃣ Configuration Tab
- **Purpose**: General bot settings
- **Features**:
  - Game window name setting
  - Performance settings (FPS, etc.)
  - Safety thresholds
  - Behavior customization
  - Import/export configuration

### 5️⃣ Logs Tab
- **Purpose**: Activity monitoring
- **Features**:
  - Real-time log display
  - Log filtering options
  - Log export functionality
  - Error highlighting

## Persistent Monitoring Panel
- Always visible regardless of active tab
- Shows current bot status
- Displays active abilities and cooldowns
- Shows performance metrics
- Provides emergency stop button