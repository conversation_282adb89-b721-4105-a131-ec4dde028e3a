#!/usr/bin/env python3
"""
Auto Attack Bot - Main Application
A configurable bot for automated ability usage based on target detection
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import json
import os
from datetime import datetime
import logging

# Import our custom modules
from src.gui.main_window import <PERSON>Window
from src.core.bot_engine import Bot<PERSON>ngine
from src.utils.config_manager import ConfigManager
from src.utils.logger import setup_logger

class AutoAttackBot:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Auto Attack Bot v1.0")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Initialize components
        self.config_manager = ConfigManager()
        self.logger = setup_logger()
        self.bot_engine = BotEngine(self.config_manager, self.logger)
        
        # Create main GUI
        self.main_window = MainWindow(
            self.root, 
            self.bot_engine, 
            self.config_manager, 
            self.logger
        )
        
        # Setup window close handler
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.logger.info("Auto Attack Bot initialized successfully")
    
    def run(self):
        """Start the application"""
        try:
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            messagebox.showerror("Error", f"Application error: {e}")
    
    def on_closing(self):
        """Handle application closing"""
        try:
            # Stop bot if running
            if self.bot_engine.is_running:
                self.bot_engine.stop()
            
            # Save current configuration
            self.config_manager.save_config()
            
            self.logger.info("Application closing gracefully")
            self.root.destroy()
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
            self.root.destroy()

if __name__ == "__main__":
    # Create necessary directories
    os.makedirs("src", exist_ok=True)
    os.makedirs("src/gui", exist_ok=True)
    os.makedirs("src/core", exist_ok=True)
    os.makedirs("src/utils", exist_ok=True)
    os.makedirs("data", exist_ok=True)
    os.makedirs("data/screenshots", exist_ok=True)
    os.makedirs("data/templates", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    # Start the application
    app = AutoAttackBot()
    app.run()
