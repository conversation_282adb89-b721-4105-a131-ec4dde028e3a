"""
Control Tab for Auto Attack Bot
Main control interface for starting/stopping the bot
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import timedelta

class ControlTab:
    def __init__(self, parent, bot_engine, config_manager, logger):
        self.parent = parent
        self.bot_engine = bot_engine
        self.config_manager = config_manager
        self.logger = logger
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Create widgets
        self.create_widgets()
        
        # Initial state
        self.update_button_states()
    
    def create_widgets(self):
        """Create control tab widgets"""
        # Configure grid
        self.frame.grid_columnconfigure(0, weight=1)
        self.frame.grid_columnconfigure(1, weight=1)
        self.frame.grid_rowconfigure(2, weight=1)
        
        # Main control section
        control_frame = ttk.LabelFrame(self.frame, text="🎮 Bot Control", padding="20")
        control_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=10, pady=10)
        control_frame.grid_columnconfigure(0, weight=1)
        control_frame.grid_columnconfigure(1, weight=1)
        control_frame.grid_columnconfigure(2, weight=1)
        
        # Start button
        self.start_button = ttk.Button(
            control_frame,
            text="▶️ START BOT",
            command=self.start_bot,
            style="Start.TButton"
        )
        self.start_button.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        
        # Pause/Resume button
        self.pause_button = ttk.Button(
            control_frame,
            text="⏸️ PAUSE",
            command=self.toggle_pause,
            state="disabled"
        )
        self.pause_button.grid(row=0, column=1, padx=10, pady=10, sticky="ew")
        
        # Stop button
        self.stop_button = ttk.Button(
            control_frame,
            text="⏹️ STOP BOT",
            command=self.stop_bot,
            state="disabled",
            style="Stop.TButton"
        )
        self.stop_button.grid(row=0, column=2, padx=10, pady=10, sticky="ew")
        
        # Status section
        status_frame = ttk.LabelFrame(self.frame, text="📊 Status & Statistics", padding="15")
        status_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=(0, 10))
        status_frame.grid_columnconfigure(1, weight=1)
        
        # Status display
        ttk.Label(status_frame, text="Current Status:", font=("Arial", 10, "bold")).grid(
            row=0, column=0, sticky="w", pady=2
        )
        self.status_display = ttk.Label(status_frame, text="Stopped", foreground="red")
        self.status_display.grid(row=0, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # Uptime
        ttk.Label(status_frame, text="Uptime:", font=("Arial", 10, "bold")).grid(
            row=1, column=0, sticky="w", pady=2
        )
        self.uptime_display = ttk.Label(status_frame, text="00:00:00")
        self.uptime_display.grid(row=1, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # Statistics section
        stats_frame = ttk.LabelFrame(self.frame, text="📈 Performance Metrics", padding="15")
        stats_frame.grid(row=1, column=1, sticky="ew", padx=(0, 10), pady=(0, 10))
        stats_frame.grid_columnconfigure(1, weight=1)
        
        # Targets detected
        ttk.Label(stats_frame, text="Targets Detected:", font=("Arial", 10, "bold")).grid(
            row=0, column=0, sticky="w", pady=2
        )
        self.targets_display = ttk.Label(stats_frame, text="0")
        self.targets_display.grid(row=0, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # Abilities used
        ttk.Label(stats_frame, text="Abilities Used:", font=("Arial", 10, "bold")).grid(
            row=1, column=0, sticky="w", pady=2
        )
        self.abilities_display = ttk.Label(stats_frame, text="0")
        self.abilities_display.grid(row=1, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # Clicks performed
        ttk.Label(stats_frame, text="Auto Clicks:", font=("Arial", 10, "bold")).grid(
            row=2, column=0, sticky="w", pady=2
        )
        self.clicks_display = ttk.Label(stats_frame, text="0")
        self.clicks_display.grid(row=2, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # Quick settings section
        quick_settings_frame = ttk.LabelFrame(self.frame, text="⚡ Quick Settings", padding="15")
        quick_settings_frame.grid(row=2, column=0, columnspan=2, sticky="nsew", padx=10, pady=(0, 10))
        quick_settings_frame.grid_columnconfigure(1, weight=1)
        
        # Auto-click toggle
        self.auto_click_var = tk.BooleanVar()
        self.auto_click_var.set(self.config_manager.get("auto_click.enabled", False))
        auto_click_check = ttk.Checkbutton(
            quick_settings_frame,
            text="Enable Auto-Click",
            variable=self.auto_click_var,
            command=self.toggle_auto_click
        )
        auto_click_check.grid(row=0, column=0, sticky="w", pady=5)
        
        # Auto-click interval
        ttk.Label(quick_settings_frame, text="Click Interval (s):").grid(
            row=0, column=1, sticky="w", padx=(20, 5), pady=5
        )
        
        interval_frame = ttk.Frame(quick_settings_frame)
        interval_frame.grid(row=0, column=2, sticky="w", pady=5)
        
        self.min_interval_var = tk.DoubleVar()
        self.min_interval_var.set(self.config_manager.get("auto_click.min_interval", 0.5))
        ttk.Label(interval_frame, text="Min:").grid(row=0, column=0)
        min_spin = ttk.Spinbox(
            interval_frame,
            from_=0.1,
            to=10.0,
            increment=0.1,
            width=6,
            textvariable=self.min_interval_var,
            command=self.update_click_intervals
        )
        min_spin.grid(row=0, column=1, padx=(2, 10))
        
        self.max_interval_var = tk.DoubleVar()
        self.max_interval_var.set(self.config_manager.get("auto_click.max_interval", 3.0))
        ttk.Label(interval_frame, text="Max:").grid(row=0, column=2)
        max_spin = ttk.Spinbox(
            interval_frame,
            from_=0.1,
            to=10.0,
            increment=0.1,
            width=6,
            textvariable=self.max_interval_var,
            command=self.update_click_intervals
        )
        max_spin.grid(row=0, column=3, padx=(2, 0))
        
        # Reset statistics button
        reset_button = ttk.Button(
            quick_settings_frame,
            text="🔄 Reset Statistics",
            command=self.reset_statistics
        )
        reset_button.grid(row=1, column=0, sticky="w", pady=(10, 0))
        
        # Test abilities button
        test_button = ttk.Button(
            quick_settings_frame,
            text="🧪 Test All Abilities",
            command=self.test_all_abilities
        )
        test_button.grid(row=1, column=1, sticky="w", padx=(20, 0), pady=(10, 0))
        
        # Setup styles
        self.setup_styles()
    
    def setup_styles(self):
        """Setup custom button styles"""
        style = ttk.Style()
        
        # Start button style
        style.configure(
            "Start.TButton",
            font=("Arial", 12, "bold")
        )
        
        # Stop button style
        style.configure(
            "Stop.TButton",
            font=("Arial", 12, "bold")
        )
    
    def start_bot(self):
        """Start the bot"""
        try:
            if self.bot_engine.start():
                self.logger.info("Bot started from control tab")
                self.update_button_states()
            else:
                messagebox.showerror("Error", "Failed to start bot. Check logs for details.")
        except Exception as e:
            self.logger.error(f"Error starting bot: {e}")
            messagebox.showerror("Error", f"Error starting bot: {e}")
    
    def stop_bot(self):
        """Stop the bot"""
        try:
            if self.bot_engine.stop():
                self.logger.info("Bot stopped from control tab")
                self.update_button_states()
            else:
                messagebox.showerror("Error", "Failed to stop bot. Check logs for details.")
        except Exception as e:
            self.logger.error(f"Error stopping bot: {e}")
            messagebox.showerror("Error", f"Error stopping bot: {e}")
    
    def toggle_pause(self):
        """Toggle bot pause state"""
        try:
            if self.bot_engine.is_paused:
                self.bot_engine.resume()
                self.pause_button.config(text="⏸️ PAUSE")
                self.logger.info("Bot resumed from control tab")
            else:
                self.bot_engine.pause()
                self.pause_button.config(text="▶️ RESUME")
                self.logger.info("Bot paused from control tab")
        except Exception as e:
            self.logger.error(f"Error toggling pause: {e}")
            messagebox.showerror("Error", f"Error toggling pause: {e}")
    
    def toggle_auto_click(self):
        """Toggle auto-click setting"""
        enabled = self.auto_click_var.get()
        self.config_manager.set("auto_click.enabled", enabled)
        self.logger.info(f"Auto-click {'enabled' if enabled else 'disabled'}")
    
    def update_click_intervals(self):
        """Update auto-click intervals"""
        min_val = self.min_interval_var.get()
        max_val = self.max_interval_var.get()
        
        # Ensure min <= max
        if min_val > max_val:
            self.max_interval_var.set(min_val)
            max_val = min_val
        
        self.config_manager.set("auto_click.min_interval", min_val)
        self.config_manager.set("auto_click.max_interval", max_val)
    
    def reset_statistics(self):
        """Reset bot statistics"""
        if messagebox.askyesno("Reset Statistics", "Are you sure you want to reset all statistics?"):
            self.bot_engine.reset_stats()
            self.logger.info("Statistics reset from control tab")
    
    def test_all_abilities(self):
        """Test all enabled abilities"""
        if messagebox.askyesno("Test Abilities", "This will test all enabled abilities. Continue?"):
            try:
                abilities = self.config_manager.get("abilities", {})
                tested_count = 0
                
                for ability_key, ability_config in abilities.items():
                    if ability_config.get("enabled", True):
                        if hasattr(self.bot_engine, 'ability_manager'):
                            self.bot_engine.ability_manager.test_ability(ability_key)
                            tested_count += 1
                
                messagebox.showinfo("Test Complete", f"Tested {tested_count} abilities.")
                self.logger.info(f"Tested {tested_count} abilities from control tab")
                
            except Exception as e:
                self.logger.error(f"Error testing abilities: {e}")
                messagebox.showerror("Error", f"Error testing abilities: {e}")
    
    def update_button_states(self):
        """Update button states based on bot status"""
        if self.bot_engine.is_running:
            self.start_button.config(state="disabled")
            self.pause_button.config(state="normal")
            self.stop_button.config(state="normal")
            
            if self.bot_engine.is_paused:
                self.pause_button.config(text="▶️ RESUME")
            else:
                self.pause_button.config(text="⏸️ PAUSE")
        else:
            self.start_button.config(state="normal")
            self.pause_button.config(state="disabled")
            self.stop_button.config(state="disabled")
            self.pause_button.config(text="⏸️ PAUSE")
    
    def update_status(self, status):
        """Update status display"""
        self.status_display.config(text=status)
        
        # Update color based on status
        if status.lower() == "running":
            self.status_display.config(foreground="green")
        elif status.lower() == "paused":
            self.status_display.config(foreground="orange")
        else:
            self.status_display.config(foreground="red")
        
        # Update button states
        self.update_button_states()
    
    def update_stats(self, stats):
        """Update statistics display"""
        self.targets_display.config(text=str(stats.get("targets_detected", 0)))
        self.abilities_display.config(text=str(stats.get("abilities_used", 0)))
        self.clicks_display.config(text=str(stats.get("clicks_performed", 0)))
        
        # Update uptime
        uptime_seconds = stats.get("uptime", 0)
        uptime_str = str(timedelta(seconds=int(uptime_seconds)))
        self.uptime_display.config(text=uptime_str)
    
    def refresh(self):
        """Refresh the control tab"""
        # Update auto-click settings from config
        self.auto_click_var.set(self.config_manager.get("auto_click.enabled", False))
        self.min_interval_var.set(self.config_manager.get("auto_click.min_interval", 0.5))
        self.max_interval_var.set(self.config_manager.get("auto_click.max_interval", 3.0))
        
        # Update button states
        self.update_button_states()
