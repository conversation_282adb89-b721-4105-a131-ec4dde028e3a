"""
Template Editor for Auto Attack Bot
Advanced template creation and region selection
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import cv2
import numpy as np
from PIL import Image, ImageTk
import base64
import io

class TemplateEditor:
    def __init__(self, parent, config_manager, logger, callback=None):
        self.parent = parent
        self.config_manager = config_manager
        self.logger = logger
        self.callback = callback
        
        # Create window
        self.window = tk.Toplevel(parent)
        self.window.title("Template Editor")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()
        
        # Variables
        self.current_image = None
        self.current_image_tk = None
        self.selection_start = None
        self.selection_end = None
        self.selection_rect = None
        
        # Create widgets
        self.create_widgets()
        
        self.logger.info("Template editor opened")
    
    def create_widgets(self):
        """Create template editor widgets"""
        # Main frame
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Top controls
        controls_frame = ttk.Frame(main_frame)
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(controls_frame, text="📁 Load Image", command=self.load_image).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(controls_frame, text="📷 Screenshot", command=self.take_screenshot).pack(side=tk.LEFT, padx=5)
        ttk.Button(controls_frame, text="💾 Save Template", command=self.save_template).pack(side=tk.RIGHT)
        
        # Image canvas frame
        canvas_frame = ttk.LabelFrame(main_frame, text="Image & Selection", padding="5")
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Canvas with scrollbars
        self.canvas = tk.Canvas(canvas_frame, bg="white", cursor="crosshair")
        h_scroll = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        v_scroll = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        
        self.canvas.configure(xscrollcommand=h_scroll.set, yscrollcommand=v_scroll.set)
        
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        h_scroll.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind canvas events
        self.canvas.bind("<Button-1>", self.start_selection)
        self.canvas.bind("<B1-Motion>", self.update_selection)
        self.canvas.bind("<ButtonRelease-1>", self.end_selection)
        
        # Template settings frame
        settings_frame = ttk.LabelFrame(main_frame, text="Template Settings", padding="10")
        settings_frame.pack(fill=tk.X)
        settings_frame.grid_columnconfigure(1, weight=1)
        settings_frame.grid_columnconfigure(3, weight=1)
        
        # Template name
        ttk.Label(settings_frame, text="Name:").grid(row=0, column=0, sticky="w", padx=(0, 5))
        self.name_var = tk.StringVar(value="New Template")
        ttk.Entry(settings_frame, textvariable=self.name_var).grid(row=0, column=1, sticky="ew", padx=(0, 20))
        
        # Assigned ability
        ttk.Label(settings_frame, text="Ability:").grid(row=0, column=2, sticky="w", padx=(0, 5))
        self.ability_var = tk.StringVar(value="ability_1")
        ability_combo = ttk.Combobox(
            settings_frame,
            textvariable=self.ability_var,
            values=["ability_1", "ability_2", "ability_3", "ability_4", "ability_5", 
                   "ability_e", "ability_x", "ability_c", "ability_q", "ability_t", "ability_r"],
            state="readonly"
        )
        ability_combo.grid(row=0, column=3, sticky="ew")
        
        # Detection method
        ttk.Label(settings_frame, text="Method:").grid(row=1, column=0, sticky="w", padx=(0, 5), pady=(5, 0))
        self.method_var = tk.StringVar(value="template")
        method_combo = ttk.Combobox(
            settings_frame,
            textvariable=self.method_var,
            values=["template", "color", "hybrid"],
            state="readonly"
        )
        method_combo.grid(row=1, column=1, sticky="ew", pady=(5, 0), padx=(0, 20))
        
        # Confidence threshold
        ttk.Label(settings_frame, text="Confidence:").grid(row=1, column=2, sticky="w", padx=(0, 5), pady=(5, 0))
        self.confidence_var = tk.DoubleVar(value=0.8)
        confidence_scale = ttk.Scale(
            settings_frame,
            from_=0.1,
            to=1.0,
            variable=self.confidence_var,
            orient=tk.HORIZONTAL
        )
        confidence_scale.grid(row=1, column=3, sticky="ew", pady=(5, 0))
        
        # Selection info
        info_frame = ttk.Frame(settings_frame)
        info_frame.grid(row=2, column=0, columnspan=4, sticky="ew", pady=(10, 0))
        
        ttk.Label(info_frame, text="Selection:").pack(side=tk.LEFT)
        self.selection_label = ttk.Label(info_frame, text="No selection")
        self.selection_label.pack(side=tk.LEFT, padx=(10, 0))
    
    def load_image(self):
        """Load an image file"""
        try:
            file_path = filedialog.askopenfilename(
                title="Select Image",
                filetypes=[
                    ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif"),
                    ("All files", "*.*")
                ]
            )
            
            if file_path:
                # Load image with OpenCV
                self.current_image = cv2.imread(file_path)
                if self.current_image is not None:
                    self.display_image()
                    self.logger.info(f"Image loaded: {file_path}")
                else:
                    messagebox.showerror("Error", "Failed to load image")
                    
        except Exception as e:
            self.logger.error(f"Error loading image: {e}")
            messagebox.showerror("Error", f"Error loading image: {e}")
    
    def take_screenshot(self):
        """Take a screenshot"""
        try:
            import pyautogui
            
            # Hide window temporarily
            self.window.withdraw()
            
            # Wait a moment
            self.window.after(1000, self._capture_screenshot)
            
        except Exception as e:
            self.logger.error(f"Error taking screenshot: {e}")
            messagebox.showerror("Error", f"Error taking screenshot: {e}")
    
    def _capture_screenshot(self):
        """Actually capture the screenshot"""
        try:
            import pyautogui
            
            # Capture screenshot
            screenshot = pyautogui.screenshot()
            
            # Convert to OpenCV format
            self.current_image = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # Show window again
            self.window.deiconify()
            
            # Display image
            self.display_image()
            
            self.logger.info("Screenshot captured")
            
        except Exception as e:
            # Make sure window is shown again
            self.window.deiconify()
            self.logger.error(f"Error capturing screenshot: {e}")
            messagebox.showerror("Error", f"Error capturing screenshot: {e}")
    
    def display_image(self):
        """Display the current image on canvas"""
        if self.current_image is None:
            return
        
        try:
            # Convert BGR to RGB for display
            image_rgb = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)
            
            # Convert to PIL Image
            pil_image = Image.fromarray(image_rgb)
            
            # Create PhotoImage
            self.current_image_tk = ImageTk.PhotoImage(pil_image)
            
            # Clear canvas and display image
            self.canvas.delete("all")
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.current_image_tk)
            
            # Update scroll region
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            
        except Exception as e:
            self.logger.error(f"Error displaying image: {e}")
    
    def start_selection(self, event):
        """Start selection rectangle"""
        if self.current_image is None:
            return
        
        # Convert canvas coordinates to image coordinates
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)
        
        self.selection_start = (int(x), int(y))
        
        # Remove previous selection
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
    
    def update_selection(self, event):
        """Update selection rectangle"""
        if self.current_image is None or self.selection_start is None:
            return
        
        # Convert canvas coordinates to image coordinates
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)
        
        # Remove previous rectangle
        if self.selection_rect:
            self.canvas.delete(self.selection_rect)
        
        # Draw new rectangle
        self.selection_rect = self.canvas.create_rectangle(
            self.selection_start[0], self.selection_start[1],
            x, y,
            outline="red", width=2
        )
    
    def end_selection(self, event):
        """End selection rectangle"""
        if self.current_image is None or self.selection_start is None:
            return
        
        # Convert canvas coordinates to image coordinates
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)
        
        self.selection_end = (int(x), int(y))
        
        # Update selection info
        if self.selection_start and self.selection_end:
            x1, y1 = self.selection_start
            x2, y2 = self.selection_end
            
            # Ensure proper order
            x1, x2 = min(x1, x2), max(x1, x2)
            y1, y2 = min(y1, y2), max(y1, y2)
            
            width = x2 - x1
            height = y2 - y1
            
            self.selection_label.config(text=f"({x1}, {y1}) - {width}x{height}")
    
    def save_template(self):
        """Save the current template"""
        if self.current_image is None:
            messagebox.showerror("Error", "No image loaded")
            return
        
        if self.selection_start is None or self.selection_end is None:
            messagebox.showerror("Error", "No region selected")
            return
        
        try:
            # Get selection coordinates
            x1, y1 = self.selection_start
            x2, y2 = self.selection_end
            
            # Ensure proper order
            x1, x2 = min(x1, x2), max(x1, x2)
            y1, y2 = min(y1, y2), max(y1, y2)
            
            # Extract template region
            template_region = self.current_image[y1:y2, x1:x2]
            
            # Convert to base64
            _, buffer = cv2.imencode('.png', template_region)
            image_data = base64.b64encode(buffer).decode('utf-8')
            
            # Create template config
            template_name = self.name_var.get()
            template_config = {
                "name": template_name,
                "image_data": image_data,
                "region": [x1, y1, x2 - x1, y2 - y1],
                "assigned_ability": self.ability_var.get(),
                "detection_method": self.method_var.get(),
                "confidence_threshold": self.confidence_var.get(),
                "enabled": True
            }
            
            # Save to config
            templates = self.config_manager.get("target_recognition.templates", {})
            templates[template_name] = template_config
            self.config_manager.set("target_recognition.templates", templates)
            self.config_manager.save_config()
            
            messagebox.showinfo("Success", f"Template '{template_name}' saved successfully!")
            self.logger.info(f"Template '{template_name}' saved")
            
            # Call callback if provided
            if self.callback:
                self.callback()
            
            # Close window
            self.window.destroy()
            
        except Exception as e:
            self.logger.error(f"Error saving template: {e}")
            messagebox.showerror("Error", f"Error saving template: {e}")
