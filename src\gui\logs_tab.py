"""
Logs Tab for Auto Attack Bot
Display and manage bot logs
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from datetime import datetime

class LogsTab:
    def __init__(self, parent, bot_engine, config_manager, logger):
        self.parent = parent
        self.bot_engine = bot_engine
        self.config_manager = config_manager
        self.logger = logger
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Log storage
        self.log_entries = []
        self.max_log_entries = 1000
        
        # Create widgets
        self.create_widgets()
        
        # Start log monitoring
        self.start_log_monitoring()
    
    def create_widgets(self):
        """Create logs tab widgets"""
        # Configure grid
        self.frame.grid_columnconfigure(0, weight=1)
        self.frame.grid_rowconfigure(1, weight=1)
        
        # Header frame
        header_frame = ttk.Frame(self.frame)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        header_frame.grid_columnconfigure(2, weight=1)
        
        ttk.Label(header_frame, text="📋 Bot Logs", font=("Arial", 14, "bold")).grid(
            row=0, column=0, sticky="w"
        )
        
        # Filter frame
        filter_frame = ttk.Frame(header_frame)
        filter_frame.grid(row=0, column=1, padx=(20, 0))
        
        ttk.Label(filter_frame, text="Filter:").grid(row=0, column=0, padx=(0, 5))
        self.filter_var = tk.StringVar(value="ALL")
        filter_combo = ttk.Combobox(
            filter_frame,
            textvariable=self.filter_var,
            values=["ALL", "DEBUG", "INFO", "WARNING", "ERROR"],
            width=10,
            state="readonly"
        )
        filter_combo.grid(row=0, column=1)
        filter_combo.bind('<<ComboboxSelected>>', self.apply_filter)
        
        # Control buttons
        ttk.Button(header_frame, text="🔄 Refresh", command=self.refresh_logs).grid(
            row=0, column=3, padx=5
        )
        ttk.Button(header_frame, text="🗑️ Clear", command=self.clear_logs).grid(
            row=0, column=4, padx=5
        )
        ttk.Button(header_frame, text="💾 Export", command=self.export_logs).grid(
            row=0, column=5, padx=5
        )
        
        # Main log display area
        log_frame = ttk.Frame(self.frame)
        log_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
        log_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(0, weight=1)
        
        # Text widget with scrollbars
        self.log_text = tk.Text(
            log_frame,
            wrap=tk.WORD,
            font=("Consolas", 9),
            bg="black",
            fg="white",
            insertbackground="white"
        )
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        h_scrollbar = ttk.Scrollbar(log_frame, orient="horizontal", command=self.log_text.xview)
        
        self.log_text.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.log_text.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Configure text tags for different log levels
        self.setup_text_tags()
        
        # Bottom status frame
        status_frame = ttk.Frame(self.frame)
        status_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=(0, 10))
        status_frame.grid_columnconfigure(1, weight=1)
        
        ttk.Label(status_frame, text="Total Entries:").grid(row=0, column=0, sticky="w")
        self.total_entries_label = ttk.Label(status_frame, text="0")
        self.total_entries_label.grid(row=0, column=1, sticky="w", padx=(10, 0))
        
        ttk.Label(status_frame, text="Log File:").grid(row=0, column=2, sticky="w", padx=(20, 0))
        self.log_file_label = ttk.Label(status_frame, text="N/A", font=("Arial", 8))
        self.log_file_label.grid(row=0, column=3, sticky="w", padx=(10, 0))
        
        # Auto-scroll checkbox
        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            status_frame,
            text="Auto-scroll",
            variable=self.auto_scroll_var
        ).grid(row=0, column=4, sticky="e", padx=(20, 0))
    
    def setup_text_tags(self):
        """Setup text tags for different log levels"""
        # DEBUG - Gray
        self.log_text.tag_configure("DEBUG", foreground="#808080")
        
        # INFO - White (default)
        self.log_text.tag_configure("INFO", foreground="#FFFFFF")
        
        # WARNING - Yellow
        self.log_text.tag_configure("WARNING", foreground="#FFFF00")
        
        # ERROR - Red
        self.log_text.tag_configure("ERROR", foreground="#FF0000")
        
        # CRITICAL - Bright Red with bold
        self.log_text.tag_configure("CRITICAL", foreground="#FF0000", font=("Consolas", 9, "bold"))
        
        # Timestamp - Light blue
        self.log_text.tag_configure("TIMESTAMP", foreground="#87CEEB")
        
        # Logger name - Green
        self.log_text.tag_configure("LOGGER", foreground="#90EE90")
    
    def start_log_monitoring(self):
        """Start monitoring logs in a separate thread"""
        def monitor_logs():
            try:
                # Get log file path from logger
                if hasattr(self.logger, 'get_log_file'):
                    log_file = self.logger.get_log_file()
                    self.log_file_label.config(text=log_file)
                    
                    # Monitor file for changes (simplified version)
                    # In a full implementation, you'd use file watching
                    self.schedule_log_refresh()
                    
            except Exception as e:
                print(f"Error starting log monitoring: {e}")
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=monitor_logs, daemon=True)
        monitor_thread.start()
    
    def schedule_log_refresh(self):
        """Schedule periodic log refresh"""
        try:
            # This is a simplified approach - in production you'd want proper file watching
            self.frame.after(2000, self.schedule_log_refresh)  # Refresh every 2 seconds
            
        except Exception as e:
            print(f"Error in log refresh scheduling: {e}")
    
    def add_log_entry(self, level, message, timestamp=None):
        """Add a log entry to the display"""
        try:
            if timestamp is None:
                timestamp = datetime.now()
            
            # Create log entry
            entry = {
                "timestamp": timestamp,
                "level": level,
                "message": message
            }
            
            # Add to storage
            self.log_entries.append(entry)
            
            # Limit number of entries
            if len(self.log_entries) > self.max_log_entries:
                self.log_entries = self.log_entries[-self.max_log_entries:]
            
            # Update display if filter matches
            if self.should_show_entry(entry):
                self.display_log_entry(entry)
            
            # Update total count
            self.total_entries_label.config(text=str(len(self.log_entries)))
            
        except Exception as e:
            print(f"Error adding log entry: {e}")
    
    def should_show_entry(self, entry):
        """Check if entry should be shown based on current filter"""
        filter_level = self.filter_var.get()
        if filter_level == "ALL":
            return True
        return entry["level"] == filter_level
    
    def display_log_entry(self, entry):
        """Display a single log entry in the text widget"""
        try:
            # Format timestamp
            timestamp_str = entry["timestamp"].strftime("%H:%M:%S")
            
            # Format the log line
            log_line = f"[{timestamp_str}] {entry['level']}: {entry['message']}\n"
            
            # Insert into text widget
            self.log_text.insert(tk.END, f"[{timestamp_str}] ", "TIMESTAMP")
            self.log_text.insert(tk.END, f"{entry['level']}", entry['level'])
            self.log_text.insert(tk.END, f": {entry['message']}\n")
            
            # Auto-scroll if enabled
            if self.auto_scroll_var.get():
                self.log_text.see(tk.END)
                
        except Exception as e:
            print(f"Error displaying log entry: {e}")
    
    def apply_filter(self, event=None):
        """Apply the current filter to the log display"""
        try:
            # Clear current display
            self.log_text.delete(1.0, tk.END)
            
            # Redisplay filtered entries
            for entry in self.log_entries:
                if self.should_show_entry(entry):
                    self.display_log_entry(entry)
                    
        except Exception as e:
            print(f"Error applying filter: {e}")
    
    def refresh_logs(self):
        """Refresh the log display"""
        try:
            # In a full implementation, this would re-read the log file
            # For now, just refresh the display
            self.apply_filter()
            
            # Add a refresh message
            self.add_log_entry("INFO", "Log display refreshed")
            
        except Exception as e:
            self.logger.error(f"Error refreshing logs: {e}")
    
    def clear_logs(self):
        """Clear all log entries"""
        if messagebox.askyesno("Clear Logs", "Are you sure you want to clear all log entries?"):
            try:
                self.log_entries.clear()
                self.log_text.delete(1.0, tk.END)
                self.total_entries_label.config(text="0")
                
                # Add clear message
                self.add_log_entry("INFO", "Log entries cleared")
                
            except Exception as e:
                self.logger.error(f"Error clearing logs: {e}")
    
    def export_logs(self):
        """Export logs to a file"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="Export Logs",
                defaultextension=".txt",
                filetypes=[
                    ("Text files", "*.txt"),
                    ("Log files", "*.log"),
                    ("All files", "*.*")
                ]
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"Auto Attack Bot Logs - Exported on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 80 + "\n\n")
                    
                    for entry in self.log_entries:
                        timestamp_str = entry["timestamp"].strftime("%Y-%m-%d %H:%M:%S")
                        f.write(f"[{timestamp_str}] {entry['level']}: {entry['message']}\n")
                
                messagebox.showinfo("Success", f"Logs exported to {file_path}")
                self.logger.info(f"Logs exported to {file_path}")
                
        except Exception as e:
            self.logger.error(f"Error exporting logs: {e}")
            messagebox.showerror("Error", f"Error exporting logs: {e}")
    
    def refresh(self):
        """Refresh the logs tab"""
        self.refresh_logs()
    
    # Methods to be called by the logger or other components
    def log_debug(self, message):
        """Add a debug log entry"""
        self.add_log_entry("DEBUG", message)
    
    def log_info(self, message):
        """Add an info log entry"""
        self.add_log_entry("INFO", message)
    
    def log_warning(self, message):
        """Add a warning log entry"""
        self.add_log_entry("WARNING", message)
    
    def log_error(self, message):
        """Add an error log entry"""
        self.add_log_entry("ERROR", message)
    
    def log_critical(self, message):
        """Add a critical log entry"""
        self.add_log_entry("CRITICAL", message)
