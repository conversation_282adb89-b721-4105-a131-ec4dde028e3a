{"version": "1.0", "last_updated": "2025-05-27T21:43:22.169896", "general": {"game_window_name": "", "monitor_fps": 30, "safety_delay": 0.1, "emergency_stop_key": "F12"}, "abilities": {"ability_1": {"name": "Quick Strike", "key": "1", "cooldown": 0.8, "priority": 5, "damage": 100, "range": 200, "press_count": 1, "hold_duration": 0.0, "is_aoe": false, "enabled": true, "random_timing": true, "timing_variance": 0.1}, "ability_2": {"name": "Power Shot", "key": "2", "cooldown": 2.5, "priority": 4, "damage": 180, "range": 350, "press_count": 1, "hold_duration": 0.0, "is_aoe": false, "enabled": true, "random_timing": true, "timing_variance": 0.3}, "ability_3": {"name": "Area Blast", "key": "3", "cooldown": 4.0, "priority": 3, "damage": 250, "range": 400, "press_count": 1, "hold_duration": 0.0, "is_aoe": true, "enabled": true, "random_timing": true, "timing_variance": 0.4}, "ability_4": {"name": "Heavy Strike", "key": "4", "cooldown": 6.0, "priority": 2, "damage": 350, "range": 250, "press_count": 1, "hold_duration": 0.0, "is_aoe": false, "enabled": true, "random_timing": true, "timing_variance": 0.5}, "ability_5": {"name": "Ultimate Devastation", "key": "5", "cooldown": 12.0, "priority": 1, "damage": 600, "range": 500, "press_count": 1, "hold_duration": 0.0, "is_aoe": true, "enabled": true, "random_timing": true, "timing_variance": 0.8}, "ability_e": {"name": "Evasion", "key": "e", "cooldown": 3.0, "priority": 4, "damage": 0, "range": 0, "press_count": 1, "hold_duration": 0.0, "is_aoe": false, "enabled": true, "random_timing": true, "timing_variance": 0.2}, "ability_x": {"name": "Explosive Burst", "key": "x", "cooldown": 8.0, "priority": 2, "damage": 450, "range": 350, "press_count": 1, "hold_duration": 0.0, "is_aoe": true, "enabled": true, "random_timing": true, "timing_variance": 0.6}, "ability_c": {"name": "Combo Attack", "key": "c", "cooldown": 5.0, "priority": 3, "damage": 200, "range": 300, "press_count": 3, "hold_duration": 0.0, "is_aoe": false, "enabled": true, "random_timing": true, "timing_variance": 0.3}, "ability_q": {"name": "Quick Cast", "key": "q", "cooldown": 4.5, "priority": 3, "damage": 220, "range": 320, "press_count": 1, "hold_duration": 0.0, "is_aoe": false, "enabled": true, "random_timing": true, "timing_variance": 0.4}, "ability_t": {"name": "Tactical Strike", "key": "t", "cooldown": 7.0, "priority": 2, "damage": 280, "range": 280, "press_count": 2, "hold_duration": 0.0, "is_aoe": false, "enabled": true, "random_timing": true, "timing_variance": 0.4}, "ability_r": {"name": "Reload/Refresh", "key": "r", "cooldown": 2.0, "priority": 5, "damage": 0, "range": 0, "press_count": 1, "hold_duration": 0.3, "is_aoe": false, "enabled": true, "random_timing": true, "timing_variance": 0.1}, "test_ability": {"name": "Test Ability", "key": "t", "cooldown": 5.0, "enabled": true}}, "auto_click": {"enabled": false, "min_interval": 0.5, "max_interval": 3.0, "click_type": "left"}, "target_recognition": {"method": "template", "confidence_threshold": 0.8, "scan_region": [0, 0, 1920, 1080], "templates": {}, "color_ranges": {}}, "performance": {"detection_fps": 10, "ability_check_interval": 0.1, "max_cpu_usage": 80}, "test": {"value": "hello_world"}}