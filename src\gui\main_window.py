"""
Main Window for Auto Attack Bot
Contains the tabbed interface and persistent monitoring panel
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time

from src.gui.control_tab import ControlTab
from src.gui.abilities_tab import AbilitiesTab
from src.gui.target_recognition_tab import Target<PERSON><PERSON>ognitionTab
from src.gui.configuration_tab import ConfigurationTab
from src.gui.logs_tab import LogsTab
from src.gui.monitoring_panel import MonitoringPanel

class MainWindow:
    def __init__(self, root, bot_engine, config_manager, logger):
        self.root = root
        self.bot_engine = bot_engine
        self.config_manager = config_manager
        self.logger = logger
        
        # Setup window
        self.setup_window()
        
        # Create main layout
        self.create_layout()
        
        # Setup callbacks
        self.setup_callbacks()
        
        self.logger.info("Main window initialized")
    
    def setup_window(self):
        """Setup main window properties"""
        self.root.title("Auto Attack Bot v1.0")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Configure grid weights
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
    
    def create_layout(self):
        """Create the main layout with monitoring panel and tabs"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Persistent monitoring panel at top
        self.monitoring_panel = MonitoringPanel(
            main_frame, 
            self.bot_engine, 
            self.config_manager, 
            self.logger
        )
        self.monitoring_panel.frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        
        # Notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky="nsew")
        
        # Create tabs
        self.create_tabs()
    
    def create_tabs(self):
        """Create all tabs"""
        # Control Tab
        self.control_tab = ControlTab(
            self.notebook, 
            self.bot_engine, 
            self.config_manager, 
            self.logger
        )
        self.notebook.add(self.control_tab.frame, text="🎮 Control")
        
        # Abilities Tab
        self.abilities_tab = AbilitiesTab(
            self.notebook, 
            self.bot_engine, 
            self.config_manager, 
            self.logger
        )
        self.notebook.add(self.abilities_tab.frame, text="⚔️ Abilities")
        
        # Target Recognition Tab
        self.target_recognition_tab = TargetRecognitionTab(
            self.notebook, 
            self.bot_engine, 
            self.config_manager, 
            self.logger
        )
        self.notebook.add(self.target_recognition_tab.frame, text="🎯 Target Recognition")
        
        # Configuration Tab
        self.configuration_tab = ConfigurationTab(
            self.notebook, 
            self.bot_engine, 
            self.config_manager, 
            self.logger
        )
        self.notebook.add(self.configuration_tab.frame, text="⚙️ Configuration")
        
        # Logs Tab
        self.logs_tab = LogsTab(
            self.notebook, 
            self.bot_engine, 
            self.config_manager, 
            self.logger
        )
        self.notebook.add(self.logs_tab.frame, text="📋 Logs")
    
    def setup_callbacks(self):
        """Setup callbacks for bot engine"""
        self.bot_engine.set_status_callback(self.on_status_update)
        self.bot_engine.set_stats_callback(self.on_stats_update)
        
        # Setup emergency stop key listener
        self.setup_emergency_stop()
    
    def setup_emergency_stop(self):
        """Setup emergency stop key listener"""
        def emergency_stop_listener():
            try:
                from pynput import keyboard
                
                def on_press(key):
                    try:
                        # Check for F12 key (default emergency stop)
                        if key == keyboard.Key.f12:
                            self.logger.warning("Emergency stop activated!")
                            self.bot_engine.emergency_stop()
                            self.show_emergency_stop_message()
                    except AttributeError:
                        pass
                
                # Start listener in background
                listener = keyboard.Listener(on_press=on_press)
                listener.daemon = True
                listener.start()
                
            except Exception as e:
                self.logger.error(f"Error setting up emergency stop: {e}")
        
        # Start emergency stop listener in background thread
        emergency_thread = threading.Thread(target=emergency_stop_listener, daemon=True)
        emergency_thread.start()
    
    def on_status_update(self, status):
        """Handle bot status updates"""
        # Update monitoring panel
        self.monitoring_panel.update_status(status)
        
        # Update control tab
        self.control_tab.update_status(status)
    
    def on_stats_update(self, stats):
        """Handle bot statistics updates"""
        # Update monitoring panel
        self.monitoring_panel.update_stats(stats)
        
        # Update control tab
        self.control_tab.update_stats(stats)
    
    def show_emergency_stop_message(self):
        """Show emergency stop message"""
        def show_message():
            messagebox.showwarning(
                "Emergency Stop", 
                "Bot has been emergency stopped!\n\nPress F12 to activate emergency stop at any time."
            )
        
        # Schedule message in main thread
        self.root.after(0, show_message)
    
    def on_closing(self):
        """Handle window closing"""
        try:
            # Stop bot if running
            if self.bot_engine.is_running:
                self.bot_engine.stop()
            
            # Save configuration
            self.config_manager.save_config()
            
            self.logger.info("Application closing")
            
        except Exception as e:
            self.logger.error(f"Error during window closing: {e}")
    
    def refresh_all_tabs(self):
        """Refresh all tabs with current data"""
        try:
            self.control_tab.refresh()
            self.abilities_tab.refresh()
            self.target_recognition_tab.refresh()
            self.configuration_tab.refresh()
            self.logs_tab.refresh()
            self.monitoring_panel.refresh()
            
        except Exception as e:
            self.logger.error(f"Error refreshing tabs: {e}")
    
    def show_tab(self, tab_name):
        """Show a specific tab"""
        tab_mapping = {
            "control": 0,
            "abilities": 1,
            "target_recognition": 2,
            "configuration": 3,
            "logs": 4
        }
        
        if tab_name.lower() in tab_mapping:
            self.notebook.select(tab_mapping[tab_name.lower()])
    
    def get_current_tab(self):
        """Get currently selected tab"""
        current_index = self.notebook.index(self.notebook.select())
        tab_names = ["control", "abilities", "target_recognition", "configuration", "logs"]
        
        if 0 <= current_index < len(tab_names):
            return tab_names[current_index]
        
        return "unknown"
