"""
Core Bot Engine for Auto Attack Bot
Handles the main bot logic, target detection, and ability execution
"""

import threading
import time
import random
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta

from src.core.target_detector import TargetDetector
from src.core.ability_manager import AbilityManager
from src.core.input_controller import Input<PERSON><PERSON>roller
from src.utils.config_manager import Config<PERSON>anager
from src.utils.logger import BotLogger

class BotEngine:
    def __init__(self, config_manager: ConfigManager, logger: BotLogger):
        self.config_manager = config_manager
        self.logger = logger
        
        # Core components
        self.target_detector = TargetDetector(config_manager, logger)
        self.ability_manager = AbilityManager(config_manager, logger)
        self.input_controller = InputController(logger)
        
        # Bot state
        self.is_running = False
        self.is_paused = False
        self.thread = None
        self.stop_event = threading.Event()
        
        # Statistics
        self.stats = {
            "start_time": None,
            "targets_detected": 0,
            "abilities_used": 0,
            "clicks_performed": 0,
            "uptime": 0
        }
        
        # Callbacks for GUI updates
        self.status_callback: Optional[Callable] = None
        self.stats_callback: Optional[Callable] = None
        
        # Auto-click settings
        self.last_auto_click = time.time()
        self.next_click_delay = self._get_random_click_delay()
        
        self.logger.info("Bot engine initialized")
    
    def set_status_callback(self, callback: Callable):
        """Set callback for status updates"""
        self.status_callback = callback
    
    def set_stats_callback(self, callback: Callable):
        """Set callback for statistics updates"""
        self.stats_callback = callback
    
    def start(self) -> bool:
        """Start the bot"""
        if self.is_running:
            self.logger.warning("Bot is already running")
            return False
        
        try:
            self.stop_event.clear()
            self.is_running = True
            self.is_paused = False
            self.stats["start_time"] = datetime.now()
            
            # Start the main bot thread
            self.thread = threading.Thread(target=self._bot_loop, daemon=True)
            self.thread.start()
            
            self.logger.log_bot_status("STARTED")
            self._update_status("Running")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start bot: {e}")
            self.is_running = False
            return False
    
    def stop(self) -> bool:
        """Stop the bot"""
        if not self.is_running:
            return True
        
        try:
            self.stop_event.set()
            self.is_running = False
            
            if self.thread and self.thread.is_alive():
                self.thread.join(timeout=2.0)
            
            self.logger.log_bot_status("STOPPED")
            self._update_status("Stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping bot: {e}")
            return False
    
    def pause(self):
        """Pause the bot"""
        self.is_paused = True
        self.logger.log_bot_status("PAUSED")
        self._update_status("Paused")
    
    def resume(self):
        """Resume the bot"""
        self.is_paused = False
        self.logger.log_bot_status("RESUMED")
        self._update_status("Running")
    
    def emergency_stop(self):
        """Emergency stop - immediately halt all operations"""
        self.logger.log_bot_status("EMERGENCY_STOP")
        self.stop()
        self.input_controller.release_all_keys()
    
    def _bot_loop(self):
        """Main bot execution loop"""
        self.logger.info("Bot loop started")
        
        while not self.stop_event.is_set() and self.is_running:
            try:
                # Check if paused
                if self.is_paused:
                    time.sleep(0.1)
                    continue
                
                # Update uptime
                if self.stats["start_time"]:
                    self.stats["uptime"] = (datetime.now() - self.stats["start_time"]).total_seconds()
                
                # Detect targets
                targets = self.target_detector.detect_targets()
                if targets:
                    self.stats["targets_detected"] += len(targets)
                    self.logger.log_target_detection(len(targets), 0.1)  # TODO: actual detection time
                    
                    # Execute abilities based on targets
                    self._execute_abilities_for_targets(targets)
                
                # Handle auto-clicking
                self._handle_auto_click()
                
                # Update statistics
                self._update_stats()
                
                # Sleep based on performance settings
                detection_fps = self.config_manager.get("performance.detection_fps", 10)
                sleep_time = 1.0 / detection_fps
                time.sleep(sleep_time)
                
            except Exception as e:
                self.logger.error(f"Error in bot loop: {e}")
                time.sleep(1.0)  # Prevent rapid error loops
        
        self.logger.info("Bot loop ended")
    
    def _execute_abilities_for_targets(self, targets: List[Dict]):
        """Execute appropriate abilities for detected targets"""
        # Get available abilities (not on cooldown)
        available_abilities = self.ability_manager.get_available_abilities()
        
        if not available_abilities:
            return
        
        # Sort abilities by priority (lower number = higher priority)
        available_abilities.sort(key=lambda x: x["priority"])
        
        for ability in available_abilities:
            if self.stop_event.is_set() or self.is_paused:
                break
            
            # Check if ability should be used for current targets
            if self._should_use_ability(ability, targets):
                success = self.ability_manager.execute_ability(ability["key"])
                if success:
                    self.stats["abilities_used"] += 1
                    self.logger.log_ability_use(ability["name"], True)
                
                # Add some delay between abilities
                time.sleep(0.1)
    
    def _should_use_ability(self, ability: Dict, targets: List[Dict]) -> bool:
        """Determine if an ability should be used based on current targets"""
        # Basic logic - can be enhanced based on specific game requirements
        
        # Check if ability is enabled
        if not ability.get("enabled", True):
            return False
        
        # For AOE abilities, prefer multiple targets
        if ability.get("is_aoe", False) and len(targets) < 2:
            return False
        
        # Check range (if target has position information)
        # This would need to be implemented based on specific target detection
        
        return True
    
    def _handle_auto_click(self):
        """Handle automatic clicking if enabled"""
        auto_click_config = self.config_manager.get("auto_click", {})
        
        if not auto_click_config.get("enabled", False):
            return
        
        current_time = time.time()
        if current_time - self.last_auto_click >= self.next_click_delay:
            # Perform auto-click
            click_type = auto_click_config.get("click_type", "left")
            self.input_controller.click(click_type)
            
            self.stats["clicks_performed"] += 1
            self.last_auto_click = current_time
            self.next_click_delay = self._get_random_click_delay()
            
            self.logger.debug(f"Auto-click performed ({click_type})")
    
    def _get_random_click_delay(self) -> float:
        """Get random delay for next auto-click"""
        auto_click_config = self.config_manager.get("auto_click", {})
        min_interval = auto_click_config.get("min_interval", 0.5)
        max_interval = auto_click_config.get("max_interval", 3.0)
        
        return random.uniform(min_interval, max_interval)
    
    def _update_status(self, status: str):
        """Update status via callback"""
        if self.status_callback:
            self.status_callback(status)
    
    def _update_stats(self):
        """Update statistics via callback"""
        if self.stats_callback:
            self.stats_callback(self.stats.copy())
    
    def get_stats(self) -> Dict:
        """Get current statistics"""
        return self.stats.copy()
    
    def reset_stats(self):
        """Reset statistics"""
        self.stats = {
            "start_time": datetime.now() if self.is_running else None,
            "targets_detected": 0,
            "abilities_used": 0,
            "clicks_performed": 0,
            "uptime": 0
        }
        self.logger.info("Statistics reset")
