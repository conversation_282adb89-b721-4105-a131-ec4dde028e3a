"""
Ability Manager for Auto Attack Bot
Handles ability cooldowns, execution, and timing
"""

import time
import random
from typing import Dict, List, Optional
from datetime import datetime, timedelta

from src.core.input_controller import InputController
from src.utils.config_manager import ConfigManager
from src.utils.logger import BotLogger

class AbilityManager:
    def __init__(self, config_manager: ConfigManager, logger: <PERSON>tLogger):
        self.config_manager = config_manager
        self.logger = logger
        self.input_controller = InputController(logger)
        
        # Track ability cooldowns
        self.ability_cooldowns = {}
        self.last_ability_use = {}
        
        # Initialize cooldown tracking
        self._initialize_abilities()
        
        self.logger.info("Ability manager initialized")
    
    def _initialize_abilities(self):
        """Initialize ability tracking"""
        abilities = self.config_manager.get("abilities", {})
        current_time = time.time()
        
        for ability_key in abilities.keys():
            self.ability_cooldowns[ability_key] = 0
            self.last_ability_use[ability_key] = current_time - 1000  # Allow immediate use
    
    def get_available_abilities(self) -> List[Dict]:
        """Get list of abilities that are off cooldown and enabled"""
        available = []
        current_time = time.time()
        abilities = self.config_manager.get("abilities", {})
        
        for ability_key, ability_config in abilities.items():
            if not ability_config.get("enabled", True):
                continue
            
            # Check if ability is off cooldown
            last_use = self.last_ability_use.get(ability_key, 0)
            cooldown = ability_config.get("cooldown", 1.0)
            
            if current_time - last_use >= cooldown:
                ability_info = ability_config.copy()
                ability_info["key"] = ability_key
                ability_info["time_until_ready"] = 0
                available.append(ability_info)
            else:
                # Calculate time until ready
                time_until_ready = cooldown - (current_time - last_use)
                self.ability_cooldowns[ability_key] = time_until_ready
        
        return available
    
    def execute_ability(self, ability_key: str) -> bool:
        """Execute an ability"""
        try:
            ability_config = self.config_manager.get_ability_config(ability_key)
            if not ability_config:
                self.logger.error(f"Ability {ability_key} not found in configuration")
                return False
            
            if not ability_config.get("enabled", True):
                self.logger.debug(f"Ability {ability_key} is disabled")
                return False
            
            # Check cooldown
            if not self._is_ability_ready(ability_key):
                self.logger.debug(f"Ability {ability_key} is on cooldown")
                return False
            
            # Add random timing variance if enabled
            if ability_config.get("random_timing", True):
                variance = ability_config.get("timing_variance", 0.2)
                delay = random.uniform(0, variance)
                time.sleep(delay)
            
            # Execute the ability
            success = self._perform_ability_input(ability_config)
            
            if success:
                # Update cooldown tracking
                self.last_ability_use[ability_key] = time.time()
                self.logger.debug(f"Ability {ability_key} executed successfully")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error executing ability {ability_key}: {e}")
            return False
    
    def _is_ability_ready(self, ability_key: str) -> bool:
        """Check if an ability is ready to use"""
        ability_config = self.config_manager.get_ability_config(ability_key)
        if not ability_config:
            return False
        
        last_use = self.last_ability_use.get(ability_key, 0)
        cooldown = ability_config.get("cooldown", 1.0)
        current_time = time.time()
        
        return current_time - last_use >= cooldown
    
    def _perform_ability_input(self, ability_config: Dict) -> bool:
        """Perform the actual input for an ability"""
        try:
            key = ability_config.get("key", "")
            press_count = ability_config.get("press_count", 1)
            hold_duration = ability_config.get("hold_duration", 0.0)
            
            if not key:
                return False
            
            # Execute based on configuration
            if hold_duration > 0:
                # Hold the key for specified duration
                self.input_controller.key_down(key)
                time.sleep(hold_duration)
                self.input_controller.key_up(key)
            else:
                # Press the key specified number of times
                for i in range(press_count):
                    self.input_controller.key_press(key)
                    if i < press_count - 1:  # Don't sleep after last press
                        time.sleep(0.05)  # Small delay between presses
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error performing ability input: {e}")
            return False
    
    def test_ability(self, ability_key: str) -> bool:
        """Test an ability (ignores cooldown)"""
        try:
            ability_config = self.config_manager.get_ability_config(ability_key)
            if not ability_config:
                return False
            
            success = self._perform_ability_input(ability_config)
            if success:
                self.logger.info(f"Test ability {ability_key} executed")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error testing ability {ability_key}: {e}")
            return False
    
    def get_ability_status(self, ability_key: str) -> Dict:
        """Get current status of an ability"""
        ability_config = self.config_manager.get_ability_config(ability_key)
        if not ability_config:
            return {}
        
        current_time = time.time()
        last_use = self.last_ability_use.get(ability_key, 0)
        cooldown = ability_config.get("cooldown", 1.0)
        
        time_since_use = current_time - last_use
        time_until_ready = max(0, cooldown - time_since_use)
        is_ready = time_until_ready <= 0
        
        return {
            "name": ability_config.get("name", ability_key),
            "key": ability_key,
            "enabled": ability_config.get("enabled", True),
            "is_ready": is_ready,
            "time_until_ready": time_until_ready,
            "cooldown": cooldown,
            "last_use": last_use,
            "priority": ability_config.get("priority", 5)
        }
    
    def get_all_abilities_status(self) -> Dict[str, Dict]:
        """Get status of all abilities"""
        abilities = self.config_manager.get("abilities", {})
        status = {}
        
        for ability_key in abilities.keys():
            status[ability_key] = self.get_ability_status(ability_key)
        
        return status
    
    def reset_cooldowns(self):
        """Reset all ability cooldowns"""
        current_time = time.time()
        for ability_key in self.last_ability_use.keys():
            self.last_ability_use[ability_key] = current_time - 1000
        
        self.logger.info("All ability cooldowns reset")
    
    def update_ability_config(self, ability_key: str, config: Dict):
        """Update configuration for an ability"""
        try:
            self.config_manager.set_ability_config(ability_key, config)
            self.logger.info(f"Ability {ability_key} configuration updated")
            return True
        except Exception as e:
            self.logger.error(f"Error updating ability config: {e}")
            return False
    
    def get_ability_priorities(self) -> List[str]:
        """Get abilities sorted by priority (highest first)"""
        abilities = self.config_manager.get("abilities", {})
        
        # Create list of (ability_key, priority) tuples
        ability_priorities = []
        for ability_key, config in abilities.items():
            if config.get("enabled", True):
                priority = config.get("priority", 5)
                ability_priorities.append((ability_key, priority))
        
        # Sort by priority (lower number = higher priority)
        ability_priorities.sort(key=lambda x: x[1])
        
        return [ability_key for ability_key, _ in ability_priorities]
    
    def get_cooldown_remaining(self, ability_key: str) -> float:
        """Get remaining cooldown time for an ability"""
        ability_config = self.config_manager.get_ability_config(ability_key)
        if not ability_config:
            return 0
        
        current_time = time.time()
        last_use = self.last_ability_use.get(ability_key, 0)
        cooldown = ability_config.get("cooldown", 1.0)
        
        return max(0, cooldown - (current_time - last_use))
