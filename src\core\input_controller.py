"""
Input Controller for Auto Attack Bot
Handles keyboard and mouse input simulation
"""

import time
import pyautogui
from pynput import keyboard, mouse
from typing import Set, Optional
import threading

from src.utils.logger import BotLogger

class InputController:
    def __init__(self, logger: <PERSON><PERSON><PERSON>ogger):
        self.logger = logger
        
        # Disable pyautogui failsafe for automated operation
        pyautogui.FAILSAFE = False
        
        # Track pressed keys for emergency release
        self.pressed_keys: Set[str] = set()
        self.key_lock = threading.Lock()
        
        # Initialize controllers
        self.keyboard_controller = keyboard.Controller()
        self.mouse_controller = mouse.Controller()
        
        self.logger.info("Input controller initialized")
    
    def key_press(self, key: str) -> bool:
        """Press and release a key"""
        try:
            # Convert string to key object if needed
            key_obj = self._string_to_key(key)
            
            with self.key_lock:
                self.keyboard_controller.press(key_obj)
                self.keyboard_controller.release(key_obj)
            
            self.logger.debug(f"Key pressed: {key}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error pressing key {key}: {e}")
            return False
    
    def key_down(self, key: str) -> bool:
        """Press and hold a key"""
        try:
            key_obj = self._string_to_key(key)
            
            with self.key_lock:
                self.keyboard_controller.press(key_obj)
                self.pressed_keys.add(key)
            
            self.logger.debug(f"Key down: {key}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error pressing key down {key}: {e}")
            return False
    
    def key_up(self, key: str) -> bool:
        """Release a key"""
        try:
            key_obj = self._string_to_key(key)
            
            with self.key_lock:
                self.keyboard_controller.release(key_obj)
                self.pressed_keys.discard(key)
            
            self.logger.debug(f"Key up: {key}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error releasing key {key}: {e}")
            return False
    
    def click(self, button: str = "left", x: Optional[int] = None, y: Optional[int] = None) -> bool:
        """Perform a mouse click"""
        try:
            # Move to position if specified
            if x is not None and y is not None:
                self.mouse_controller.position = (x, y)
                time.sleep(0.01)  # Small delay for movement
            
            # Determine button
            if button.lower() == "left":
                mouse_button = mouse.Button.left
            elif button.lower() == "right":
                mouse_button = mouse.Button.right
            elif button.lower() == "middle":
                mouse_button = mouse.Button.middle
            else:
                mouse_button = mouse.Button.left
            
            # Perform click
            self.mouse_controller.click(mouse_button)
            
            self.logger.debug(f"Mouse click: {button} at {self.mouse_controller.position}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error clicking mouse: {e}")
            return False
    
    def double_click(self, button: str = "left", x: Optional[int] = None, y: Optional[int] = None) -> bool:
        """Perform a double click"""
        try:
            # Move to position if specified
            if x is not None and y is not None:
                self.mouse_controller.position = (x, y)
                time.sleep(0.01)
            
            # Determine button
            if button.lower() == "left":
                mouse_button = mouse.Button.left
            elif button.lower() == "right":
                mouse_button = mouse.Button.right
            elif button.lower() == "middle":
                mouse_button = mouse.Button.middle
            else:
                mouse_button = mouse.Button.left
            
            # Perform double click
            self.mouse_controller.click(mouse_button, 2)
            
            self.logger.debug(f"Mouse double click: {button} at {self.mouse_controller.position}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error double clicking mouse: {e}")
            return False
    
    def scroll(self, dx: int = 0, dy: int = 0) -> bool:
        """Scroll the mouse wheel"""
        try:
            self.mouse_controller.scroll(dx, dy)
            self.logger.debug(f"Mouse scroll: dx={dx}, dy={dy}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error scrolling mouse: {e}")
            return False
    
    def move_mouse(self, x: int, y: int) -> bool:
        """Move mouse to position"""
        try:
            self.mouse_controller.position = (x, y)
            self.logger.debug(f"Mouse moved to: ({x}, {y})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error moving mouse: {e}")
            return False
    
    def get_mouse_position(self) -> tuple:
        """Get current mouse position"""
        return self.mouse_controller.position
    
    def release_all_keys(self):
        """Release all currently pressed keys (emergency function)"""
        try:
            with self.key_lock:
                for key in self.pressed_keys.copy():
                    key_obj = self._string_to_key(key)
                    self.keyboard_controller.release(key_obj)
                
                self.pressed_keys.clear()
            
            self.logger.info("All keys released")
            
        except Exception as e:
            self.logger.error(f"Error releasing all keys: {e}")
    
    def _string_to_key(self, key_string: str):
        """Convert string representation to pynput key object"""
        # Handle special keys
        special_keys = {
            'space': keyboard.Key.space,
            'enter': keyboard.Key.enter,
            'tab': keyboard.Key.tab,
            'shift': keyboard.Key.shift,
            'ctrl': keyboard.Key.ctrl,
            'alt': keyboard.Key.alt,
            'esc': keyboard.Key.esc,
            'escape': keyboard.Key.esc,
            'backspace': keyboard.Key.backspace,
            'delete': keyboard.Key.delete,
            'home': keyboard.Key.home,
            'end': keyboard.Key.end,
            'page_up': keyboard.Key.page_up,
            'page_down': keyboard.Key.page_down,
            'up': keyboard.Key.up,
            'down': keyboard.Key.down,
            'left': keyboard.Key.left,
            'right': keyboard.Key.right,
            'f1': keyboard.Key.f1,
            'f2': keyboard.Key.f2,
            'f3': keyboard.Key.f3,
            'f4': keyboard.Key.f4,
            'f5': keyboard.Key.f5,
            'f6': keyboard.Key.f6,
            'f7': keyboard.Key.f7,
            'f8': keyboard.Key.f8,
            'f9': keyboard.Key.f9,
            'f10': keyboard.Key.f10,
            'f11': keyboard.Key.f11,
            'f12': keyboard.Key.f12,
        }
        
        key_lower = key_string.lower()
        
        if key_lower in special_keys:
            return special_keys[key_lower]
        else:
            # Regular character key
            return key_string.lower()
    
    def type_text(self, text: str, delay: float = 0.01) -> bool:
        """Type text with optional delay between characters"""
        try:
            for char in text:
                self.keyboard_controller.type(char)
                if delay > 0:
                    time.sleep(delay)
            
            self.logger.debug(f"Text typed: {text}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error typing text: {e}")
            return False
    
    def key_combination(self, *keys) -> bool:
        """Press a combination of keys (e.g., Ctrl+C)"""
        try:
            key_objects = [self._string_to_key(key) for key in keys]
            
            # Press all keys
            for key_obj in key_objects:
                self.keyboard_controller.press(key_obj)
            
            # Small delay
            time.sleep(0.01)
            
            # Release all keys in reverse order
            for key_obj in reversed(key_objects):
                self.keyboard_controller.release(key_obj)
            
            self.logger.debug(f"Key combination pressed: {'+'.join(keys)}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error pressing key combination: {e}")
            return False
