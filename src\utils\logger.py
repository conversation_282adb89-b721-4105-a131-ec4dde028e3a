"""
Logging utility for Auto Attack Bot
Provides structured logging with file and console output
"""

import logging
import os
from datetime import datetime
from typing import Optional

class BotLogger:
    def __init__(self, name: str = "AutoAttackBot", log_file: Optional[str] = None):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Create formatters
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_formatter = logging.Formatter(
            '%(levelname)s: %(message)s'
        )
        
        # File handler
        if log_file is None:
            log_file = f"logs/bot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        self.log_file = log_file
    
    def debug(self, message: str):
        self.logger.debug(message)
    
    def info(self, message: str):
        self.logger.info(message)
    
    def warning(self, message: str):
        self.logger.warning(message)
    
    def error(self, message: str):
        self.logger.error(message)
    
    def critical(self, message: str):
        self.logger.critical(message)
    
    def log_ability_use(self, ability_name: str, target_found: bool = True):
        """Log ability usage"""
        status = "SUCCESS" if target_found else "NO_TARGET"
        self.info(f"ABILITY_USE: {ability_name} - {status}")
    
    def log_target_detection(self, target_count: int, detection_time: float):
        """Log target detection results"""
        self.debug(f"TARGET_DETECTION: Found {target_count} targets in {detection_time:.3f}s")
    
    def log_bot_status(self, status: str, details: str = ""):
        """Log bot status changes"""
        message = f"BOT_STATUS: {status}"
        if details:
            message += f" - {details}"
        self.info(message)
    
    def get_log_file(self) -> str:
        """Get the current log file path"""
        return self.log_file

def setup_logger(name: str = "AutoAttackBot", log_file: Optional[str] = None) -> BotLogger:
    """Setup and return a configured logger"""
    return BotLogger(name, log_file)
