"""
Persistent Monitoring Panel for Auto Attack Bot
Always visible panel showing bot status and key metrics
"""

import tkinter as tk
from tkinter import ttk
import time
from datetime import timedelta

class MonitoringPanel:
    def __init__(self, parent, bot_engine, config_manager, logger):
        self.parent = parent
        self.bot_engine = bot_engine
        self.config_manager = config_manager
        self.logger = logger
        
        # Create main frame
        self.frame = ttk.LabelFrame(parent, text="🔍 Bot Monitor", padding="10")
        
        # Create widgets
        self.create_widgets()
        
        # Start update loop
        self.update_display()
    
    def create_widgets(self):
        """Create monitoring panel widgets"""
        # Configure grid
        self.frame.grid_columnconfigure(1, weight=1)
        self.frame.grid_columnconfigure(3, weight=1)
        self.frame.grid_columnconfigure(5, weight=1)
        
        # Status section
        ttk.Label(self.frame, text="Status:", font=("Arial", 10, "bold")).grid(
            row=0, column=0, sticky="w", padx=(0, 5)
        )
        self.status_label = ttk.Label(self.frame, text="Stopped", foreground="red")
        self.status_label.grid(row=0, column=1, sticky="w")
        
        # Uptime section
        ttk.Label(self.frame, text="Uptime:", font=("Arial", 10, "bold")).grid(
            row=0, column=2, sticky="w", padx=(20, 5)
        )
        self.uptime_label = ttk.Label(self.frame, text="00:00:00")
        self.uptime_label.grid(row=0, column=3, sticky="w")
        
        # Emergency stop button
        self.emergency_button = ttk.Button(
            self.frame, 
            text="🛑 EMERGENCY STOP", 
            command=self.emergency_stop,
            style="Emergency.TButton"
        )
        self.emergency_button.grid(row=0, column=4, sticky="e", padx=(20, 0))
        
        # Second row - Statistics
        ttk.Label(self.frame, text="Targets:", font=("Arial", 9)).grid(
            row=1, column=0, sticky="w", pady=(5, 0)
        )
        self.targets_label = ttk.Label(self.frame, text="0")
        self.targets_label.grid(row=1, column=1, sticky="w", pady=(5, 0))
        
        ttk.Label(self.frame, text="Abilities:", font=("Arial", 9)).grid(
            row=1, column=2, sticky="w", padx=(20, 5), pady=(5, 0)
        )
        self.abilities_label = ttk.Label(self.frame, text="0")
        self.abilities_label.grid(row=1, column=3, sticky="w", pady=(5, 0))
        
        ttk.Label(self.frame, text="Clicks:", font=("Arial", 9)).grid(
            row=1, column=4, sticky="w", padx=(20, 5), pady=(5, 0)
        )
        self.clicks_label = ttk.Label(self.frame, text="0")
        self.clicks_label.grid(row=1, column=5, sticky="w", pady=(5, 0))
        
        # Third row - Active abilities
        ttk.Label(self.frame, text="Active Abilities:", font=("Arial", 9, "bold")).grid(
            row=2, column=0, sticky="w", pady=(5, 0), columnspan=2
        )
        
        # Frame for ability status indicators
        self.abilities_frame = ttk.Frame(self.frame)
        self.abilities_frame.grid(row=2, column=2, columnspan=4, sticky="ew", pady=(5, 0))
        
        # Create ability status indicators
        self.ability_indicators = {}
        self.create_ability_indicators()
        
        # Configure emergency button style
        self.setup_styles()
    
    def setup_styles(self):
        """Setup custom styles"""
        style = ttk.Style()
        
        # Emergency button style
        style.configure(
            "Emergency.TButton",
            foreground="white",
            background="red",
            font=("Arial", 10, "bold")
        )
    
    def create_ability_indicators(self):
        """Create indicators for each ability"""
        abilities = self.config_manager.get("abilities", {})
        
        col = 0
        for ability_key, ability_config in abilities.items():
            if ability_config.get("enabled", True):
                # Create indicator frame
                indicator_frame = ttk.Frame(self.abilities_frame)
                indicator_frame.grid(row=0, column=col, padx=2)
                
                # Key label
                key_label = ttk.Label(
                    indicator_frame, 
                    text=ability_config.get("key", ability_key).upper(),
                    font=("Arial", 8, "bold"),
                    width=3,
                    anchor="center"
                )
                key_label.grid(row=0, column=0)
                
                # Status indicator (colored circle)
                status_label = ttk.Label(
                    indicator_frame,
                    text="●",
                    font=("Arial", 12),
                    foreground="green"
                )
                status_label.grid(row=1, column=0)
                
                # Cooldown label
                cooldown_label = ttk.Label(
                    indicator_frame,
                    text="0.0s",
                    font=("Arial", 7),
                    width=4,
                    anchor="center"
                )
                cooldown_label.grid(row=2, column=0)
                
                self.ability_indicators[ability_key] = {
                    "frame": indicator_frame,
                    "key_label": key_label,
                    "status_label": status_label,
                    "cooldown_label": cooldown_label
                }
                
                col += 1
    
    def update_status(self, status):
        """Update bot status display"""
        self.status_label.config(text=status)
        
        # Update status color
        if status.lower() == "running":
            self.status_label.config(foreground="green")
        elif status.lower() == "paused":
            self.status_label.config(foreground="orange")
        else:
            self.status_label.config(foreground="red")
    
    def update_stats(self, stats):
        """Update statistics display"""
        # Update counters
        self.targets_label.config(text=str(stats.get("targets_detected", 0)))
        self.abilities_label.config(text=str(stats.get("abilities_used", 0)))
        self.clicks_label.config(text=str(stats.get("clicks_performed", 0)))
        
        # Update uptime
        uptime_seconds = stats.get("uptime", 0)
        uptime_str = str(timedelta(seconds=int(uptime_seconds)))
        self.uptime_label.config(text=uptime_str)
    
    def update_ability_status(self):
        """Update ability status indicators"""
        if hasattr(self.bot_engine, 'ability_manager'):
            abilities_status = self.bot_engine.ability_manager.get_all_abilities_status()
            
            for ability_key, indicator in self.ability_indicators.items():
                if ability_key in abilities_status:
                    status = abilities_status[ability_key]
                    
                    # Update status color
                    if status["is_ready"]:
                        indicator["status_label"].config(foreground="green")
                        indicator["cooldown_label"].config(text="Ready")
                    else:
                        indicator["status_label"].config(foreground="red")
                        cooldown_time = status["time_until_ready"]
                        indicator["cooldown_label"].config(text=f"{cooldown_time:.1f}s")
    
    def emergency_stop(self):
        """Handle emergency stop button"""
        self.bot_engine.emergency_stop()
        self.logger.warning("Emergency stop activated from monitoring panel")
    
    def update_display(self):
        """Periodic update of the display"""
        try:
            # Update ability status
            self.update_ability_status()
            
            # Schedule next update
            self.frame.after(500, self.update_display)  # Update every 500ms
            
        except Exception as e:
            self.logger.error(f"Error updating monitoring panel: {e}")
            # Still schedule next update to prevent stopping
            self.frame.after(1000, self.update_display)
    
    def refresh(self):
        """Refresh the monitoring panel"""
        try:
            # Recreate ability indicators if configuration changed
            for indicator in self.ability_indicators.values():
                indicator["frame"].destroy()
            
            self.ability_indicators.clear()
            self.create_ability_indicators()
            
        except Exception as e:
            self.logger.error(f"Error refreshing monitoring panel: {e}")
    
    def show_ability_details(self, ability_key):
        """Show detailed information about an ability"""
        if hasattr(self.bot_engine, 'ability_manager'):
            status = self.bot_engine.ability_manager.get_ability_status(ability_key)
            
            details = f"""Ability: {status.get('name', ability_key)}
Key: {status.get('key', 'Unknown')}
Enabled: {status.get('enabled', False)}
Ready: {status.get('is_ready', False)}
Cooldown: {status.get('cooldown', 0):.1f}s
Time Until Ready: {status.get('time_until_ready', 0):.1f}s
Priority: {status.get('priority', 5)}"""
            
            # Create tooltip or popup with details
            # This could be enhanced with a proper tooltip widget
            print(details)  # For now, just print to console
