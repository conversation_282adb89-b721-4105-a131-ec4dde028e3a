"""
Configuration Tab for Auto Attack Bot
General bot settings and configuration management
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json

class ConfigurationTab:
    def __init__(self, parent, bot_engine, config_manager, logger):
        self.parent = parent
        self.bot_engine = bot_engine
        self.config_manager = config_manager
        self.logger = logger
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Create widgets
        self.create_widgets()
        
        # Load current settings
        self.load_settings()
    
    def create_widgets(self):
        """Create configuration tab widgets"""
        # Configure grid
        self.frame.grid_columnconfigure(0, weight=1)
        self.frame.grid_columnconfigure(1, weight=1)
        self.frame.grid_rowconfigure(1, weight=1)
        
        # Header frame
        header_frame = ttk.Frame(self.frame)
        header_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=10, pady=10)
        header_frame.grid_columnconfigure(2, weight=1)
        
        ttk.Label(header_frame, text="⚙️ Configuration", font=("Arial", 14, "bold")).grid(
            row=0, column=0, sticky="w"
        )
        
        # Control buttons
        ttk.Button(header_frame, text="💾 Save", command=self.save_settings).grid(
            row=0, column=3, padx=5
        )
        ttk.Button(header_frame, text="📤 Export", command=self.export_config).grid(
            row=0, column=4, padx=5
        )
        ttk.Button(header_frame, text="📥 Import", command=self.import_config).grid(
            row=0, column=5, padx=5
        )
        
        # Left panel - General Settings
        general_frame = ttk.LabelFrame(self.frame, text="General Settings", padding="15")
        general_frame.grid(row=1, column=0, sticky="nsew", padx=(10, 5), pady=(0, 10))
        general_frame.grid_columnconfigure(1, weight=1)
        
        # Game window name
        ttk.Label(general_frame, text="Game Window Name:").grid(row=0, column=0, sticky="w", pady=5)
        self.window_name_var = tk.StringVar()
        ttk.Entry(general_frame, textvariable=self.window_name_var, width=30).grid(
            row=0, column=1, sticky="ew", padx=(10, 0), pady=5
        )
        
        # Monitor FPS
        ttk.Label(general_frame, text="Monitor FPS:").grid(row=1, column=0, sticky="w", pady=5)
        self.monitor_fps_var = tk.IntVar(value=30)
        ttk.Spinbox(
            general_frame,
            from_=1,
            to=60,
            textvariable=self.monitor_fps_var,
            width=10
        ).grid(row=1, column=1, sticky="w", padx=(10, 0), pady=5)
        
        # Safety delay
        ttk.Label(general_frame, text="Safety Delay (s):").grid(row=2, column=0, sticky="w", pady=5)
        self.safety_delay_var = tk.DoubleVar(value=0.1)
        ttk.Spinbox(
            general_frame,
            from_=0.01,
            to=1.0,
            increment=0.01,
            textvariable=self.safety_delay_var,
            width=10
        ).grid(row=2, column=1, sticky="w", padx=(10, 0), pady=5)
        
        # Emergency stop key
        ttk.Label(general_frame, text="Emergency Stop Key:").grid(row=3, column=0, sticky="w", pady=5)
        self.emergency_key_var = tk.StringVar(value="F12")
        emergency_combo = ttk.Combobox(
            general_frame,
            textvariable=self.emergency_key_var,
            values=["F12", "F11", "F10", "F9", "ESC"],
            width=10,
            state="readonly"
        )
        emergency_combo.grid(row=3, column=1, sticky="w", padx=(10, 0), pady=5)
        
        # Performance settings
        perf_frame = ttk.LabelFrame(general_frame, text="Performance", padding="10")
        perf_frame.grid(row=4, column=0, columnspan=2, sticky="ew", pady=(15, 0))
        perf_frame.grid_columnconfigure(1, weight=1)
        
        # Detection FPS
        ttk.Label(perf_frame, text="Detection FPS:").grid(row=0, column=0, sticky="w", pady=2)
        self.detection_fps_var = tk.IntVar(value=10)
        ttk.Spinbox(
            perf_frame,
            from_=1,
            to=30,
            textvariable=self.detection_fps_var,
            width=10
        ).grid(row=0, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # Ability check interval
        ttk.Label(perf_frame, text="Ability Check Interval (s):").grid(row=1, column=0, sticky="w", pady=2)
        self.ability_interval_var = tk.DoubleVar(value=0.1)
        ttk.Spinbox(
            perf_frame,
            from_=0.01,
            to=1.0,
            increment=0.01,
            textvariable=self.ability_interval_var,
            width=10
        ).grid(row=1, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # Max CPU usage
        ttk.Label(perf_frame, text="Max CPU Usage (%):").grid(row=2, column=0, sticky="w", pady=2)
        self.max_cpu_var = tk.IntVar(value=80)
        ttk.Spinbox(
            perf_frame,
            from_=10,
            to=100,
            textvariable=self.max_cpu_var,
            width=10
        ).grid(row=2, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # Right panel - Advanced Settings
        advanced_frame = ttk.LabelFrame(self.frame, text="Advanced Settings", padding="15")
        advanced_frame.grid(row=1, column=1, sticky="nsew", padx=(5, 10), pady=(0, 10))
        advanced_frame.grid_columnconfigure(1, weight=1)
        
        # Scan region settings
        region_frame = ttk.LabelFrame(advanced_frame, text="Scan Region", padding="10")
        region_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 15))
        region_frame.grid_columnconfigure(1, weight=1)
        region_frame.grid_columnconfigure(3, weight=1)
        
        # X, Y, Width, Height
        ttk.Label(region_frame, text="X:").grid(row=0, column=0, sticky="w", pady=2)
        self.scan_x_var = tk.IntVar(value=0)
        ttk.Spinbox(region_frame, from_=0, to=3840, textvariable=self.scan_x_var, width=8).grid(
            row=0, column=1, sticky="w", padx=(5, 10), pady=2
        )
        
        ttk.Label(region_frame, text="Y:").grid(row=0, column=2, sticky="w", pady=2)
        self.scan_y_var = tk.IntVar(value=0)
        ttk.Spinbox(region_frame, from_=0, to=2160, textvariable=self.scan_y_var, width=8).grid(
            row=0, column=3, sticky="w", padx=(5, 0), pady=2
        )
        
        ttk.Label(region_frame, text="Width:").grid(row=1, column=0, sticky="w", pady=2)
        self.scan_width_var = tk.IntVar(value=1920)
        ttk.Spinbox(region_frame, from_=100, to=3840, textvariable=self.scan_width_var, width=8).grid(
            row=1, column=1, sticky="w", padx=(5, 10), pady=2
        )
        
        ttk.Label(region_frame, text="Height:").grid(row=1, column=2, sticky="w", pady=2)
        self.scan_height_var = tk.IntVar(value=1080)
        ttk.Spinbox(region_frame, from_=100, to=2160, textvariable=self.scan_height_var, width=8).grid(
            row=1, column=3, sticky="w", padx=(5, 0), pady=2
        )
        
        # Auto-click advanced settings
        autoclick_frame = ttk.LabelFrame(advanced_frame, text="Auto-Click Advanced", padding="10")
        autoclick_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 15))
        autoclick_frame.grid_columnconfigure(1, weight=1)
        
        # Click type
        ttk.Label(autoclick_frame, text="Click Type:").grid(row=0, column=0, sticky="w", pady=2)
        self.click_type_var = tk.StringVar(value="left")
        click_combo = ttk.Combobox(
            autoclick_frame,
            textvariable=self.click_type_var,
            values=["left", "right", "middle"],
            width=10,
            state="readonly"
        )
        click_combo.grid(row=0, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # Random pattern checkbox
        self.random_pattern_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            autoclick_frame,
            text="Use Random Pattern",
            variable=self.random_pattern_var
        ).grid(row=1, column=0, columnspan=2, sticky="w", pady=5)
        
        # Logging settings
        logging_frame = ttk.LabelFrame(advanced_frame, text="Logging", padding="10")
        logging_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(0, 15))
        logging_frame.grid_columnconfigure(1, weight=1)
        
        # Log level
        ttk.Label(logging_frame, text="Log Level:").grid(row=0, column=0, sticky="w", pady=2)
        self.log_level_var = tk.StringVar(value="INFO")
        log_combo = ttk.Combobox(
            logging_frame,
            textvariable=self.log_level_var,
            values=["DEBUG", "INFO", "WARNING", "ERROR"],
            width=10,
            state="readonly"
        )
        log_combo.grid(row=0, column=1, sticky="w", padx=(10, 0), pady=2)
        
        # Auto-save logs
        self.auto_save_logs_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            logging_frame,
            text="Auto-save Logs",
            variable=self.auto_save_logs_var
        ).grid(row=1, column=0, columnspan=2, sticky="w", pady=2)
        
        # Configuration info
        info_frame = ttk.LabelFrame(advanced_frame, text="Configuration Info", padding="10")
        info_frame.grid(row=3, column=0, columnspan=2, sticky="ew")
        
        # Config file path
        ttk.Label(info_frame, text="Config File:", font=("Arial", 9, "bold")).grid(
            row=0, column=0, sticky="w", pady=2
        )
        self.config_path_label = ttk.Label(info_frame, text="data/config.json", font=("Arial", 8))
        self.config_path_label.grid(row=1, column=0, sticky="w")
        
        # Last updated
        ttk.Label(info_frame, text="Last Updated:", font=("Arial", 9, "bold")).grid(
            row=2, column=0, sticky="w", pady=(10, 2)
        )
        self.last_updated_label = ttk.Label(info_frame, text="Never", font=("Arial", 8))
        self.last_updated_label.grid(row=3, column=0, sticky="w")
    
    def save_settings(self):
        """Save all configuration settings"""
        try:
            # General settings
            self.config_manager.set("general.game_window_name", self.window_name_var.get())
            self.config_manager.set("general.monitor_fps", self.monitor_fps_var.get())
            self.config_manager.set("general.safety_delay", self.safety_delay_var.get())
            self.config_manager.set("general.emergency_stop_key", self.emergency_key_var.get())
            
            # Performance settings
            self.config_manager.set("performance.detection_fps", self.detection_fps_var.get())
            self.config_manager.set("performance.ability_check_interval", self.ability_interval_var.get())
            self.config_manager.set("performance.max_cpu_usage", self.max_cpu_var.get())
            
            # Scan region
            scan_region = [
                self.scan_x_var.get(),
                self.scan_y_var.get(),
                self.scan_width_var.get(),
                self.scan_height_var.get()
            ]
            self.config_manager.set("target_recognition.scan_region", scan_region)
            
            # Auto-click settings
            self.config_manager.set("auto_click.click_type", self.click_type_var.get())
            
            # Save to file
            self.config_manager.save_config()
            
            messagebox.showinfo("Success", "Configuration saved successfully!")
            self.logger.info("Configuration saved from configuration tab")
            
            # Update last updated label
            self.update_info_labels()
            
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            messagebox.showerror("Error", f"Error saving configuration: {e}")
    
    def load_settings(self):
        """Load settings from configuration"""
        try:
            # General settings
            self.window_name_var.set(self.config_manager.get("general.game_window_name", ""))
            self.monitor_fps_var.set(self.config_manager.get("general.monitor_fps", 30))
            self.safety_delay_var.set(self.config_manager.get("general.safety_delay", 0.1))
            self.emergency_key_var.set(self.config_manager.get("general.emergency_stop_key", "F12"))
            
            # Performance settings
            self.detection_fps_var.set(self.config_manager.get("performance.detection_fps", 10))
            self.ability_interval_var.set(self.config_manager.get("performance.ability_check_interval", 0.1))
            self.max_cpu_var.set(self.config_manager.get("performance.max_cpu_usage", 80))
            
            # Scan region
            scan_region = self.config_manager.get("target_recognition.scan_region", [0, 0, 1920, 1080])
            self.scan_x_var.set(scan_region[0])
            self.scan_y_var.set(scan_region[1])
            self.scan_width_var.set(scan_region[2])
            self.scan_height_var.set(scan_region[3])
            
            # Auto-click settings
            self.click_type_var.set(self.config_manager.get("auto_click.click_type", "left"))
            
            # Update info labels
            self.update_info_labels()
            
        except Exception as e:
            self.logger.error(f"Error loading settings: {e}")
    
    def update_info_labels(self):
        """Update configuration info labels"""
        try:
            # Config file path
            self.config_path_label.config(text=self.config_manager.config_file)
            
            # Last updated
            last_updated = self.config_manager.get("last_updated", "Never")
            if last_updated != "Never":
                # Format the datetime string
                from datetime import datetime
                dt = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
                formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                self.last_updated_label.config(text=formatted_time)
            else:
                self.last_updated_label.config(text="Never")
                
        except Exception as e:
            self.logger.error(f"Error updating info labels: {e}")
    
    def export_config(self):
        """Export configuration to file"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="Export Configuration",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if file_path:
                if self.config_manager.export_config(file_path):
                    messagebox.showinfo("Success", f"Configuration exported to {file_path}")
                    self.logger.info(f"Configuration exported to {file_path}")
                else:
                    messagebox.showerror("Error", "Failed to export configuration")
                    
        except Exception as e:
            self.logger.error(f"Error exporting configuration: {e}")
            messagebox.showerror("Error", f"Error exporting configuration: {e}")
    
    def import_config(self):
        """Import configuration from file"""
        try:
            file_path = filedialog.askopenfilename(
                title="Import Configuration",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if file_path:
                if messagebox.askyesno("Import Configuration", 
                                     "This will overwrite current settings. Continue?"):
                    if self.config_manager.import_config(file_path):
                        self.load_settings()
                        messagebox.showinfo("Success", f"Configuration imported from {file_path}")
                        self.logger.info(f"Configuration imported from {file_path}")
                    else:
                        messagebox.showerror("Error", "Failed to import configuration")
                        
        except Exception as e:
            self.logger.error(f"Error importing configuration: {e}")
            messagebox.showerror("Error", f"Error importing configuration: {e}")
    
    def refresh(self):
        """Refresh the configuration tab"""
        self.load_settings()
