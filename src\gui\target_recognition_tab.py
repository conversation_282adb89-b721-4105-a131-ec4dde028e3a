"""
Target Recognition Tab for Auto Attack Bot
Configure target detection settings and templates
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import base64
from PIL import Image, ImageTk
import io

class TargetRecognitionTab:
    def __init__(self, parent, bot_engine, config_manager, logger):
        self.parent = parent
        self.bot_engine = bot_engine
        self.config_manager = config_manager
        self.logger = logger

        # Create main frame
        self.frame = ttk.Frame(parent)

        # Create widgets
        self.create_widgets()

        # Load current settings
        self.load_settings()

    def create_widgets(self):
        """Create target recognition tab widgets"""
        # Configure grid
        self.frame.grid_columnconfigure(0, weight=1)
        self.frame.grid_columnconfigure(1, weight=1)
        self.frame.grid_rowconfigure(1, weight=1)

        # Header frame
        header_frame = ttk.Frame(self.frame)
        header_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=10, pady=10)

        ttk.Label(header_frame, text="🎯 Target Recognition", font=("Arial", 14, "bold")).grid(
            row=0, column=0, sticky="w"
        )

        # Left panel - Settings
        settings_frame = ttk.LabelFrame(self.frame, text="Detection Settings", padding="10")
        settings_frame.grid(row=1, column=0, sticky="nsew", padx=(10, 5), pady=(0, 10))
        settings_frame.grid_columnconfigure(1, weight=1)

        # Detection method
        ttk.Label(settings_frame, text="Detection Method:").grid(row=0, column=0, sticky="w", pady=5)
        self.method_var = tk.StringVar(value="template")
        method_combo = ttk.Combobox(
            settings_frame,
            textvariable=self.method_var,
            values=["template", "color", "hybrid"],
            state="readonly"
        )
        method_combo.grid(row=0, column=1, sticky="ew", padx=(10, 0), pady=5)

        # Confidence threshold
        ttk.Label(settings_frame, text="Confidence Threshold:").grid(row=1, column=0, sticky="w", pady=5)
        self.confidence_var = tk.DoubleVar(value=0.8)
        confidence_scale = ttk.Scale(
            settings_frame,
            from_=0.1,
            to=1.0,
            variable=self.confidence_var,
            orient="horizontal"
        )
        confidence_scale.grid(row=1, column=1, sticky="ew", padx=(10, 0), pady=5)

        # Confidence value label
        self.confidence_label = ttk.Label(settings_frame, text="0.8")
        self.confidence_label.grid(row=1, column=2, padx=(5, 0), pady=5)

        # Update confidence label when scale changes
        confidence_scale.configure(command=self.update_confidence_label)

        # Screenshot upload section
        upload_frame = ttk.LabelFrame(settings_frame, text="Screenshot Management", padding="10")
        upload_frame.grid(row=2, column=0, columnspan=3, sticky="ew", pady=(10, 0))
        upload_frame.grid_columnconfigure(1, weight=1)

        ttk.Button(upload_frame, text="📷 Upload Screenshot", command=self.upload_screenshot).grid(
            row=0, column=0, pady=5
        )
        ttk.Button(upload_frame, text="🖼️ Take Screenshot", command=self.take_screenshot).grid(
            row=0, column=1, padx=(10, 0), pady=5
        )

        # Test detection button
        ttk.Button(settings_frame, text="🧪 Test Detection", command=self.test_detection).grid(
            row=3, column=0, columnspan=3, pady=(20, 0), sticky="ew"
        )

        # Right panel - Templates and preview
        preview_frame = ttk.LabelFrame(self.frame, text="Templates & Preview", padding="10")
        preview_frame.grid(row=1, column=1, sticky="nsew", padx=(5, 10), pady=(0, 10))
        preview_frame.grid_columnconfigure(0, weight=1)
        preview_frame.grid_rowconfigure(1, weight=1)

        # Template list
        template_list_frame = ttk.Frame(preview_frame)
        template_list_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        template_list_frame.grid_columnconfigure(0, weight=1)

        ttk.Label(template_list_frame, text="Saved Templates:", font=("Arial", 10, "bold")).grid(
            row=0, column=0, sticky="w"
        )

        # Template listbox with scrollbar
        list_frame = ttk.Frame(template_list_frame)
        list_frame.grid(row=1, column=0, sticky="ew", pady=(5, 0))
        list_frame.grid_columnconfigure(0, weight=1)

        self.template_listbox = tk.Listbox(list_frame, height=6)
        template_scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.template_listbox.yview)
        self.template_listbox.configure(yscrollcommand=template_scrollbar.set)

        self.template_listbox.grid(row=0, column=0, sticky="ew")
        template_scrollbar.grid(row=0, column=1, sticky="ns")

        # Template buttons
        template_buttons_frame = ttk.Frame(template_list_frame)
        template_buttons_frame.grid(row=2, column=0, sticky="ew", pady=(5, 0))

        ttk.Button(template_buttons_frame, text="➕ Add", command=self.add_template).grid(
            row=0, column=0, padx=(0, 5)
        )
        ttk.Button(template_buttons_frame, text="❌ Remove", command=self.remove_template).grid(
            row=0, column=1, padx=5
        )
        ttk.Button(template_buttons_frame, text="✏️ Edit", command=self.edit_template).grid(
            row=0, column=2, padx=(5, 0)
        )

        # Preview area
        preview_canvas_frame = ttk.Frame(preview_frame)
        preview_canvas_frame.grid(row=1, column=0, sticky="nsew")
        preview_canvas_frame.grid_columnconfigure(0, weight=1)
        preview_canvas_frame.grid_rowconfigure(0, weight=1)

        self.preview_canvas = tk.Canvas(preview_canvas_frame, bg="white", height=300)
        preview_h_scroll = ttk.Scrollbar(preview_canvas_frame, orient="horizontal", command=self.preview_canvas.xview)
        preview_v_scroll = ttk.Scrollbar(preview_canvas_frame, orient="vertical", command=self.preview_canvas.yview)

        self.preview_canvas.configure(
            xscrollcommand=preview_h_scroll.set,
            yscrollcommand=preview_v_scroll.set
        )

        self.preview_canvas.grid(row=0, column=0, sticky="nsew")
        preview_h_scroll.grid(row=1, column=0, sticky="ew")
        preview_v_scroll.grid(row=0, column=1, sticky="ns")

        # Bind template selection
        self.template_listbox.bind('<<ListboxSelect>>', self.on_template_select)

    def update_confidence_label(self, value):
        """Update confidence threshold label"""
        self.confidence_label.config(text=f"{float(value):.2f}")

    def upload_screenshot(self):
        """Upload a screenshot file"""
        try:
            file_path = filedialog.askopenfilename(
                title="Select Screenshot",
                filetypes=[
                    ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                # Load and process image
                with open(file_path, 'rb') as f:
                    image_data = base64.b64encode(f.read()).decode('utf-8')

                # Save to templates (this is a simplified version)
                # In a full implementation, you'd want to show the image and let user select regions
                messagebox.showinfo("Success", "Screenshot uploaded successfully!")
                self.logger.info(f"Screenshot uploaded: {file_path}")

        except Exception as e:
            self.logger.error(f"Error uploading screenshot: {e}")
            messagebox.showerror("Error", f"Error uploading screenshot: {e}")

    def take_screenshot(self):
        """Take a screenshot of the current screen"""
        try:
            import pyautogui

            # Hide window temporarily
            self.frame.master.master.withdraw()

            # Wait a moment for window to hide
            self.frame.after(500, self._capture_screenshot)

        except Exception as e:
            self.logger.error(f"Error taking screenshot: {e}")
            messagebox.showerror("Error", f"Error taking screenshot: {e}")

    def _capture_screenshot(self):
        """Actually capture the screenshot"""
        try:
            import pyautogui

            # Capture screenshot
            screenshot = pyautogui.screenshot()

            # Show window again
            self.frame.master.master.deiconify()

            # Convert to base64
            buffer = io.BytesIO()
            screenshot.save(buffer, format='PNG')
            image_data = base64.b64encode(buffer.getvalue()).decode('utf-8')

            # For now, just show success message
            # In full implementation, show image for region selection
            messagebox.showinfo("Success", "Screenshot captured successfully!")
            self.logger.info("Screenshot captured")

        except Exception as e:
            # Make sure window is shown again
            self.frame.master.master.deiconify()
            self.logger.error(f"Error capturing screenshot: {e}")
            messagebox.showerror("Error", f"Error capturing screenshot: {e}")

    def test_detection(self):
        """Test current detection settings"""
        try:
            if hasattr(self.bot_engine, 'target_detector'):
                targets, screenshot = self.bot_engine.target_detector.test_detection()

                if targets:
                    message = f"Detection test successful!\nFound {len(targets)} targets."
                    for i, target in enumerate(targets[:5]):  # Show first 5
                        message += f"\nTarget {i+1}: {target.get('name', 'Unknown')} at {target.get('position', 'Unknown')}"

                    messagebox.showinfo("Test Results", message)
                    self.logger.info(f"Detection test found {len(targets)} targets")
                else:
                    messagebox.showinfo("Test Results", "No targets detected.")
                    self.logger.info("Detection test found no targets")
            else:
                messagebox.showerror("Error", "Target detector not available")

        except Exception as e:
            self.logger.error(f"Error testing detection: {e}")
            messagebox.showerror("Error", f"Error testing detection: {e}")

    def add_template(self):
        """Add a new template"""
        try:
            from src.gui.template_editor import TemplateEditor

            # Open template editor
            editor = TemplateEditor(
                self.frame.master.master,  # Main window
                self.config_manager,
                self.logger,
                callback=self.load_templates  # Refresh templates when saved
            )

        except Exception as e:
            self.logger.error(f"Error opening template editor: {e}")
            messagebox.showerror("Error", f"Error opening template editor: {e}")

    def remove_template(self):
        """Remove selected template"""
        selection = self.template_listbox.curselection()
        if selection:
            template_name = self.template_listbox.get(selection[0])
            if messagebox.askyesno("Remove Template", f"Are you sure you want to remove template '{template_name}'?"):
                # Remove from config
                templates = self.config_manager.get("target_recognition.templates", {})
                if template_name in templates:
                    del templates[template_name]
                    self.config_manager.set("target_recognition.templates", templates)
                    self.load_templates()
                    self.logger.info(f"Template '{template_name}' removed")

    def edit_template(self):
        """Edit selected template"""
        selection = self.template_listbox.curselection()
        if selection:
            template_name = self.template_listbox.get(selection[0])
            messagebox.showinfo("Edit Template", f"Template editor for '{template_name}' would open here.\nThis feature will be implemented in the full version.")

    def on_template_select(self, event):
        """Handle template selection"""
        selection = self.template_listbox.curselection()
        if selection:
            template_name = self.template_listbox.get(selection[0])
            self.show_template_preview(template_name)

    def show_template_preview(self, template_name):
        """Show preview of selected template"""
        try:
            templates = self.config_manager.get("target_recognition.templates", {})
            if template_name in templates:
                template_data = templates[template_name]

                # Clear canvas
                self.preview_canvas.delete("all")

                # Show template info
                info_text = f"Template: {template_name}\nMethod: {template_data.get('detection_method', 'template')}\nConfidence: {template_data.get('confidence_threshold', 0.8)}"
                self.preview_canvas.create_text(10, 10, text=info_text, anchor="nw", font=("Arial", 10))

        except Exception as e:
            self.logger.error(f"Error showing template preview: {e}")

    def load_settings(self):
        """Load current settings from configuration"""
        try:
            # Load detection method
            method = self.config_manager.get("target_recognition.method", "template")
            self.method_var.set(method)

            # Load confidence threshold
            confidence = self.config_manager.get("target_recognition.confidence_threshold", 0.8)
            self.confidence_var.set(confidence)
            self.confidence_label.config(text=f"{confidence:.2f}")

            # Load templates
            self.load_templates()

        except Exception as e:
            self.logger.error(f"Error loading settings: {e}")

    def load_templates(self):
        """Load templates into listbox"""
        try:
            self.template_listbox.delete(0, tk.END)

            templates = self.config_manager.get("target_recognition.templates", {})
            for template_name in templates.keys():
                self.template_listbox.insert(tk.END, template_name)

        except Exception as e:
            self.logger.error(f"Error loading templates: {e}")

    def save_settings(self):
        """Save current settings to configuration"""
        try:
            self.config_manager.set("target_recognition.method", self.method_var.get())
            self.config_manager.set("target_recognition.confidence_threshold", self.confidence_var.get())
            self.config_manager.save_config()

            self.logger.info("Target recognition settings saved")

        except Exception as e:
            self.logger.error(f"Error saving settings: {e}")

    def refresh(self):
        """Refresh the target recognition tab"""
        self.load_settings()
